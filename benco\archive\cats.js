// Import necessary modules
const puppeteer = require('puppeteer');
const fs = require('fs');
const readline = require('readline');
const path = require('path'); // Import path module for resolving directory

// Define the path for user data directory
// This will create a 'puppeteer_user_data' folder in the same directory as your script
const userDataDir = path.resolve(__dirname, 'puppeteer_user_data');

// Function to prompt the user for input in the terminal
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

async function scrapeBenco() {
    let browser;
    try {
        console.log(`Launching browser with user data directory: ${userDataDir}`);
        // Launch a new browser instance with user data directory
        // Set headless to false to see the browser UI, true for background execution
        browser = await puppeteer.launch({
            headless: false,
            userDataDir: userDataDir // This will save/load your session
        });
        const page = await browser.newPage();

        // Set a default timeout for navigation to avoid endless waiting
        page.setDefaultNavigationTimeout(60000); // 60 seconds

        console.log('Navigating to https://shop.benco.com/Browse...');
        await page.goto('https://shop.benco.com/Browse', { waitUntil: 'networkidle2' });

        // Add a short delay to allow session data to fully load and apply
        console.log('Waiting 5 seconds for page to fully render with session data...');
        // FIX: Replaced page.waitForTimeout with a standard JavaScript setTimeout in a Promise
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for 5 seconds

        // Check if the user is already logged in by looking for a common element
        // IMPORTANT: You might need to adjust this selector ('.user-account-info')
        // based on Benco's actual logged-in state. Look for an element that *only*
        // appears when you are logged in (e.g., "Welcome, [Your Name]", "My Account" link).
        const isLoggedIn = await page.$('.user-account-info');
        if (!isLoggedIn) {
            console.log('Login status: NOT logged in. Please log in manually in the browser.');
            // Wait for user to confirm login
            await askQuestion('Press Enter in this terminal AFTER you have successfully logged in to the website and the page has loaded. ');
            console.log('Login confirmed. Your session should now be saved for future runs.');
        } else {
            console.log('Login status: Logged in. Session detected. Skipping manual login.');
        }

        // Wait for at least one tab-container to be present after login
        await page.waitForSelector('.tab-container', { visible: true, timeout: 30000 });
        console.log('Tab container(s) found. Starting data extraction...');

        // Scrape the data
        const categories = await page.evaluate(() => {
            const catArray = [];
            // Select ALL div elements with class "tab-container"
            const tabContainers = document.querySelectorAll('.tab-container');

            tabContainers.forEach(tabContainer => {
                const ulElement = tabContainer.querySelector('ul');
                if (ulElement) {
                    const liElements = ulElement.querySelectorAll('li');
                    liElements.forEach(li => {
                        const anchor = li.querySelector('a'); // Assuming the href is on an <a> tag inside the <li>
                        if (anchor) {
                            const catUrl = anchor.getAttribute('href');
                            const catName = anchor.innerText.trim();
                            catArray.push({ 'cat-url': catUrl, 'cat-name': catName });
                        }
                    });
                }
            });
            return catArray;
        });

        console.log('Scraping complete. Found categories:', categories.length);

        // Save the data to a JSON file
        fs.writeFileSync('cats.json', JSON.stringify(categories, null, 2), 'utf8');
        console.log('Data successfully saved to cats.json');

    } catch (error) {
        console.error('An error occurred:', error);
    } finally {
        // Close the browser
        if (browser) {
            await browser.close();
            console.log('Browser closed.');
        }
        rl.close(); // Close the readline interface
    }
}

// Run the scraper
scrapeBenco();
