// Import the Node.js file system module for file operations
const fs = require('fs').promises;
const path = require('path');

/**
 * Processes a single JSON file, extracts specified properties, renames them,
 * and separates records based on whether the 'mpn' (renamed 'mfr') is null.
 *
 * @param {object} config - Configuration object for the file to process.
 * @param {string} config.inputFileName - The name of the input JSON file (e.g., 'dds.json').
 * @param {string} config.outputPrefix - The prefix for output files (e.g., 'dds').
 * @param {object} config.propertyMap - An object mapping original property names to new names.
 * Example: { mpn: 'mfr', brand: 'manufacturer' }
 */
async function processJsonFile(config) {
    const { inputFileName, outputPrefix, propertyMap } = config;

    const outputUpFileName = `${outputPrefix}_up.json`;
    const outputNullFileName = `${outputPrefix}_null.json`;

    const inputFilePath = path.join(__dirname, inputFileName);
    const outputUpFilePath = path.join(__dirname, outputUpFileName);
    const outputNullFilePath = path.join(__dirname, outputNullFileName);

    let originalProducts = [];
    let extractedProducts = [];
    let nullMfrProducts = [];

    try {
        // 1. Read the input JSON file
        console.log(`\n--- Processing ${inputFileName} ---`);
        console.log(`Reading data from ${inputFileName}...`);
        const data = await fs.readFile(inputFilePath, 'utf8');

        // 2. Parse its content as a JSON array
        originalProducts = JSON.parse(data);
        console.log(`Successfully read ${originalProducts.length} products from ${inputFileName}.`);

        // 3. Iterate through each object and extract/rename specified properties
        originalProducts.forEach(product => {
            const newProduct = {};
            let isMfrNull = false; // Flag to track if the 'mfr' (renamed mpn) is null

            // Apply property mapping and extraction
            for (const originalProp in propertyMap) {
                const newProp = propertyMap[originalProp];
                // Use product[originalProp] directly, if it's undefined, it will be null in newProduct
                const value = product[originalProp] !== undefined ? product[originalProp] : null;

                newProduct[newProp] = value;

                // Check if the property being mapped is 'mpn' and its value is null/undefined
                if (originalProp === 'mpn' && (value === null || value === undefined)) {
                    isMfrNull = true;
                }
            }

            // Separate products based on whether 'mfr' (renamed mpn) is null
            if (isMfrNull) {
                nullMfrProducts.push(newProduct);
            } else {
                extractedProducts.push(newProduct);
            }
        });

        // 4. Save the results to respective JSON files
        console.log(`Saving successfully extracted data to ${outputUpFileName}...`);
        await fs.writeFile(outputUpFilePath, JSON.stringify(extractedProducts, null, 2), 'utf8');
        console.log(`Successfully saved ${extractedProducts.length} products to ${outputUpFileName}.`);

        if (nullMfrProducts.length > 0) {
            console.log(`Saving objects with null 'mfr' to ${outputNullFileName}...`);
            await fs.writeFile(outputNullFilePath, JSON.stringify(nullMfrProducts, null, 2), 'utf8');
            console.log(`Successfully saved ${nullMfrProducts.length} products to ${outputNullFileName}.`);
        } else {
            console.log(`No objects with null 'mfr' found for ${inputFileName}. Skipping ${outputNullFileName} creation.`);
        }

        // 5. Log the total number of objects processed
        console.log(`Total objects processed for ${inputFileName}: ${originalProducts.length}`);
        console.log(`Objects with 'mfr' extracted: ${extractedProducts.length}`);
        console.log(`Objects with null 'mfr': ${nullMfrProducts.length}`);

    } catch (error) {
        console.error(`An error occurred while processing ${inputFileName}: ${error.message}`);
        if (error.code === 'ENOENT') {
            console.error(`Error: The file '${inputFileName}' was not found. Please make sure it's in the same directory as the script.`);
        }
    }
}

// --- Main execution block ---
async function main() {
    const fileConfigurations = [
        {
            inputFileName: 'net32.json',
            outputPrefix: 'net32',
            // Corrected propertyMap: 'brand' from input becomes 'manufacturer' in output
            propertyMap: { mpn: 'mfr', brand: 'manufacturer', name: 'name', url: 'url', maincat: 'maincat' }
        },
        {
            inputFileName: 'prewest.json',
            outputPrefix: 'midwest',
            // Corrected propertyMap: 'brand' from input becomes 'manufacturer' in output
            propertyMap: { mpn: 'mfr', brand: 'manufacturer', name: 'name', url: 'url', maincat: 'maincat' }
        },
        {
            inputFileName: 'safco.json',
            outputPrefix: 'safco',
            // Corrected propertyMap: 'brand' from input becomes 'manufacturer' in output
            propertyMap: { mpn: 'mfr', brand: 'manufacturer', name: 'name', url: 'url', maincat: 'maincat' }
        },
        {
            inputFileName: 'tdsc.json',
            outputPrefix: 'tdsc',
            // Corrected propertyMap: 'brand' from input becomes 'manufacturer' in output
            propertyMap: { mpn: 'mfr', brand: 'manufacturer', name: 'name', url: 'url', maincat: 'maincat' }
        },
        // If darby_updated.json also uses 'brand' instead of 'manufacturer' for the brand name,
        // you would adjust its propertyMap similarly if you add it back here.
        // {
        //     inputFileName: 'darby_updated.json',
        //     outputPrefix: 'darby',
        //     propertyMap: { mfr: 'mfr', manufacturer: 'brand', name: 'name', url: 'url', maincat: 'maincat' }
        // }
    ];

    for (const config of fileConfigurations) {
        await processJsonFile(config);
    }
    console.log('\nAll specified files have been processed.');
}

// Execute the main function
main();
