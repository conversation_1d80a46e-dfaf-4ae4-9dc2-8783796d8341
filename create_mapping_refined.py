#!/usr/bin/env python3
"""
Refined script to create standardized manufacturer mapping from uniquebrand.json
Uses more conservative grouping to avoid over-aggregation.
"""

import json
import re
from collections import defaultdict
from pathlib import Path

def normalize_for_comparison(name):
    """Normalize manufacturer name for comparison purposes."""
    # Convert to lowercase
    name = name.lower()
    # Remove common suffixes and prefixes but be more conservative
    name = re.sub(r'\b(inc\.?|corp\.?|corporation|company|co\.?|ltd\.?|llc|llp)\b', '', name)
    # Remove punctuation and extra spaces
    name = re.sub(r'[^\w\s]', ' ', name)
    name = re.sub(r'\s+', ' ', name).strip()
    return name

def create_standardized_name(name):
    """Create a standardized name following the pattern from ubrands.json."""
    # Start with the original name
    std_name = name.lower()

    # Remove common business suffixes
    std_name = re.sub(r'\b(inc\.?|corp\.?|corporation|company|co\.?|ltd\.?|llc|llp|international|intl\.?|usa|america|american)\b', '', std_name)

    # Remove "the", "a", "an"
    std_name = re.sub(r'\b(the|a|an)\b', '', std_name)

    # Remove punctuation except hyphens
    std_name = re.sub(r'[^\w\s\-]', '', std_name)

    # Clean up spaces and convert to single word or hyphenated
    words = std_name.split()
    if len(words) == 1:
        std_name = words[0]
    elif len(words) == 2:
        # For two words, concatenate if both are short, otherwise hyphenate
        if len(words[0]) <= 4 and len(words[1]) <= 4:
            std_name = ''.join(words)
        else:
            std_name = '-'.join(words)
    else:
        # For multiple words, take first word if meaningful, otherwise first two
        if len(words[0]) >= 4:
            std_name = words[0]
        else:
            std_name = ''.join(words[:2])

    # Handle special cases
    if std_name.startswith('3m'):
        return '3msolventum'
    elif std_name.startswith('3d') and 'dental' in name.lower():
        return '3ddental'
    elif std_name.startswith('3shape'):
        return '3shape'

    return std_name

def are_very_similar(name1, name2):
    """More conservative similarity check - only group very similar names."""
    norm1 = normalize_for_comparison(name1)
    norm2 = normalize_for_comparison(name2)

    # Exact match after normalization
    if norm1 == norm2:
        return True

    # Check for exact substring matches with minimum length
    if len(norm1) >= 6 and len(norm2) >= 6:
        if norm1 in norm2 or norm2 in norm1:
            # Additional check: make sure it's not just a common word
            shorter = norm1 if len(norm1) < len(norm2) else norm2
            if shorter not in ['medical', 'dental', 'products', 'health', 'care', 'systems']:
                return True

    # Check for very specific patterns
    # Remove spaces and compare
    clean1 = re.sub(r'\s+', '', norm1)
    clean2 = re.sub(r'\s+', '', norm2)

    if clean1 == clean2:
        return True

    # Check for abbreviation patterns (more strict)
    if is_likely_abbreviation(name1, name2):
        return True

    return False

def is_likely_abbreviation(name1, name2):
    """Check if one name is likely an abbreviation of another."""
    # Clean names
    clean1 = re.sub(r'[^\w]', '', name1.lower())
    clean2 = re.sub(r'[^\w]', '', name2.lower())

    # Determine which is shorter
    if len(clean1) < len(clean2):
        short, long = clean1, clean2
        short_orig, long_orig = name1, name2
    else:
        short, long = clean2, clean1
        short_orig, long_orig = name2, name1

    # Only consider if short name is 2-6 characters and significantly shorter
    if not (2 <= len(short) <= 6 and len(long) >= len(short) * 2):
        return False

    # Extract first letters of words from long name
    words = re.findall(r'\b\w', long_orig.lower())
    if len(words) >= len(short):
        initials = ''.join(words[:len(short)])
        if initials == short:
            return True

    # Check if short name appears at the beginning of long name
    if long.startswith(short) and len(short) >= 4:
        return True

    return False

def find_conservative_groups(names):
    """Group manufacturer names using conservative similarity matching."""
    groups = []
    used = set()

    for name in names:
        if name in used:
            continue

        # Start a new group with this name
        group = [name]
        used.add(name)

        # Find very similar names
        for other_name in names:
            if other_name in used:
                continue

            if are_very_similar(name, other_name):
                group.append(other_name)
                used.add(other_name)

        groups.append(group)

    return groups

def load_existing_mapping():
    """Load existing mapping from prototype/ubrands.json for reference."""
    try:
        with open('prototype/ubrands.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Warning: prototype/ubrands.json not found, proceeding without reference")
        return {}

def choose_best_standard_name(group, existing_mapping):
    """Choose the best standardized name for a group."""
    # First, check if any name in the group exists in the existing mapping
    for name in group:
        if name in existing_mapping:
            return existing_mapping[name]

    # If no existing mapping, choose the best representative
    # Prefer shorter, cleaner names
    candidates = []
    for name in group:
        score = 0

        # Prefer shorter names
        score -= len(name) * 0.1

        # Penalize excessive punctuation
        punct_count = len(re.findall(r'[^\w\s]', name))
        score -= punct_count * 2

        # Penalize all-caps names (unless they're short)
        if name.isupper() and len(name) > 5:
            score -= 3

        # Prefer names that look like proper company names
        if re.search(r'\b(dental|medical|health|corp|inc)\b', name.lower()):
            score += 1

        # Prefer names without numbers (unless it's a meaningful part like 3M)
        if re.search(r'\d', name) and not name.startswith(('3M', '3D', '2XL')):
            score -= 1

        candidates.append((score, name))

    # Sort by score and take the best
    candidates.sort(reverse=True)
    best_name = candidates[0][1]

    # Create standardized version
    return create_standardized_name(best_name)

def create_refined_mapping():
    """Main function to create the refined manufacturer mapping."""
    # Load unique manufacturer names
    try:
        with open('uniquebrand.json', 'r', encoding='utf-8') as f:
            unique_names = json.load(f)
    except FileNotFoundError:
        print("Error: uniquebrand.json not found. Please run extract_manufacturers.py first.")
        return

    print(f"Loaded {len(unique_names)} unique manufacturer names")

    # Load existing mapping for reference
    existing_mapping = load_existing_mapping()
    print(f"Loaded {len(existing_mapping)} existing mappings for reference")

    # Group similar names using conservative approach
    print("Grouping similar manufacturer names (conservative approach)...")
    groups = find_conservative_groups(unique_names)

    print(f"Created {len(groups)} groups from {len(unique_names)} names")

    # Create the mapping
    mapping = {}

    for i, group in enumerate(groups):
        if i % 200 == 0:
            print(f"Processing group {i+1}/{len(groups)}")

        # Choose the best standardized name for this group
        standard_name = choose_best_standard_name(group, existing_mapping)

        # Map all names in the group to the standard name
        for name in group:
            mapping[name] = standard_name

    # Save the mapping
    output_file = "map_refined.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, indent=2, ensure_ascii=False)

        print(f"\nRefined mapping saved to: {output_file}")
        print(f"Total mappings: {len(mapping)}")
        print(f"Unique standardized names: {len(set(mapping.values()))}")

        # Show some examples
        print("\nSample mappings:")
        sample_count = 0
        for original, standardized in mapping.items():
            if sample_count >= 15:
                break
            print(f"  '{original}' -> '{standardized}'")
            sample_count += 1

        # Show some groups with multiple members
        print("\nSample groups with multiple members:")
        group_count = 0
        for group in groups:
            if len(group) > 1 and group_count < 10:
                standard_name = mapping[group[0]]
                print(f"  Group '{standard_name}': {group}")
                group_count += 1

    except Exception as e:
        print(f"Error saving mapping to {output_file}: {e}")

if __name__ == "__main__":
    create_refined_mapping()