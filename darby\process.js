// Import the Node.js file system module for file operations
const fs = require('fs').promises;
const path = require('path');

async function extractDarbyUpData() {
    const inputFileName = 'darby_updated.json';
    const outputFileName = 'darby_up.json';

    const inputFilePath = path.join(__dirname, inputFileName);
    const outputFilePath = path.join(__dirname, outputFileName);

    let originalProducts = [];
    let extractedProducts = [];

    try {
        // 1. Read the darby_updated.json file
        console.log(`Reading data from ${inputFileName}...`);
        const data = await fs.readFile(inputFilePath, 'utf8');

        // 2. Parse its content as a JSON array
        originalProducts = JSON.parse(data);
        console.log(`Successfully read ${originalProducts.length} products from ${inputFileName}.`);

        // 3. Iterate through each object and extract specified properties
        originalProducts.forEach(product => {
            extractedProducts.push({
                mfr: product.mfr || null, // Ensure mfr is included, default to null if not present
                name: product.name || null, // Ensure name is included, default to null if not present
                url: product.url || null,   // Ensure url is included, default to null if not present
                brand: product.manufacturer || null, // Using 'manufacturer' for 'brand' as per discussion
                maincat: product.maincat || null // Ensure maincat is included, default to null if not present
            });
        });

        // 4. Save the results to darby_up.json
        console.log(`Saving extracted data to ${outputFileName}...`);
        await fs.writeFile(outputFilePath, JSON.stringify(extractedProducts, null, 2), 'utf8');
        console.log(`Successfully saved ${extractedProducts.length} products to ${outputFileName}.`);

        // 5. Log the total number of objects processed
        console.log(`Total objects processed and extracted: ${extractedProducts.length}`);

    } catch (error) {
        console.error(`An error occurred: ${error.message}`);
        if (error.code === 'ENOENT') {
            console.error(`Error: The file '${inputFileName}' was not found. Please make sure it's in the same directory as the script.`);
        } else {
            console.error(`An unexpected error occurred: ${error.message}`);
        }
    }
}

// Execute the function
extractDarbyUpData();
