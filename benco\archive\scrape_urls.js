// Import necessary modules
const puppeteer = require('puppeteer');
const fs = require('fs');
const readline = require('readline');
const path = require('path');

// Define paths for user data and output files
const userDataDir = path.resolve(__dirname, 'puppeteer_user_data');
const catsFilePath = path.resolve(__dirname, 'cats.json');
const productsFilePath = path.resolve(__dirname, 'benco_products_urls.json');
const progressFilePath = path.resolve(__dirname, 'progress.json');

// Function to prompt the user for input in the terminal (only used for closing rl interface)
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Function to load JSON file
function loadJsonFile(filePath, defaultValue = {}) {
    if (fs.existsSync(filePath)) {
        try {
            return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        } catch (error) {
            console.error(`Error reading ${filePath}:`, error);
            return defaultValue;
        }
    }
    return defaultValue;
}

// Function to save JSON file
function saveJsonFile(filePath, data) {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
}

/**
 * Helper function to retry an asynchronous operation on TimeoutError.
 * @param {Function} operation - The asynchronous function to execute.
 * @param {number} maxRetries - Maximum number of retries.
 * @param {number} delayMs - Delay in milliseconds between retries.
 * @returns {Promise<any>} The result of the operation.
 * @throws {Error} If the operation fails after all retries or for a non-TimeoutError.
 */
async function retryOperation(operation, maxRetries = 3, delayMs = 5000) {
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            if (error.name === 'TimeoutError') {
                console.warn(`Operation timed out (attempt ${i + 1}/${maxRetries + 1}). Retrying in ${delayMs / 1000} seconds...`);
                if (i < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, delayMs)); // Wait before retrying
                } else {
                    throw new Error(`Operation failed after ${maxRetries + 1} attempts due to TimeoutError.`);
                }
            } else {
                throw error; // Re-throw other types of errors immediately
            }
        }
    }
}

async function scrapeBencoProducts() {
    let browser;
    // Load existing product data and progress data
    let productsData = loadJsonFile(productsFilePath);
    let progressData = loadJsonFile(progressFilePath, { last_overall_category_url: null });

    try {
        console.log(`Launching browser with user data directory: ${userDataDir}`);
        browser = await puppeteer.launch({
            headless: false, // Set to true for background execution
            userDataDir: userDataDir
        });
        const page = await browser.newPage();
        page.setDefaultNavigationTimeout(60000); // 60 seconds timeout for navigation

        const categories = loadJsonFile(catsFilePath, []);
        if (categories.length === 0) {
            console.error('No categories found in cats.json. Please run the previous script to generate it first.');
            return;
        }

        let resumeCategoryIndex = 0;
        let initialPageToResumeFrom = 1;

        const productDataKeys = Object.keys(productsData);
        if (productDataKeys.length > 0) {
            const lastScrapedCategoryName = productDataKeys[productDataKeys.length - 1];
            const productCountInLastCategory = productsData[lastScrapedCategoryName].length;

            // Find the category object in 'categories' array based on its name
            const lastScrapedCategoryObj = categories.find(cat => cat['cat-name'] === lastScrapedCategoryName);

            if (lastScrapedCategoryObj) {
                resumeCategoryIndex = categories.indexOf(lastScrapedCategoryObj);
                initialPageToResumeFrom = Math.floor(productCountInLastCategory / 24) + 1;

                // Adjust initialPageToResumeFrom if the last page was fully scraped
                if (productCountInLastCategory > 0 && productCountInLastCategory % 24 === 0) {
                    initialPageToResumeFrom++;
                    console.log(`Last category '${lastScrapedCategoryName}' had a full last page (${productCountInLastCategory} products). Resuming from page ${initialPageToResumeFrom}.`);
                } else if (productCountInLastCategory > 0 && productCountInLastCategory % 24 !== 0) {
                    console.log(`Last category '${lastScrapedCategoryName}' was partially scraped (${productCountInLastCategory} products). Resuming from page ${initialPageToResumeFrom} to re-process the last partial page.`);
                } else {
                    console.log(`Last category '${lastScrapedCategoryName}' has 0 products. Resuming from page 1.`);
                    initialPageToResumeFrom = 1;
                }
            } else {
                console.warn(`Last scraped category name '${lastScrapedCategoryName}' not found in cats.json. Starting from the first category.`);
                resumeCategoryIndex = 0;
                initialPageToResumeFrom = 1;
            }
            console.log(`Resuming from category: ${categories[resumeCategoryIndex]['cat-name']} at page: ${initialPageToResumeFrom}`);
        } else {
            console.log('No previous product data found, starting from the first category and page 1.');
        }

        // Iterate through each category
        for (let i = resumeCategoryIndex; i < categories.length; i++) {
            const category = categories[i];
            const categoryName = category['cat-name'];
            const categoryUrl = category['cat-url'];

            console.log(`\n--- Processing Category: ${categoryName} ---`);
            console.log(`URL: ${categoryUrl}`);

            // Initialize category in productsData if it doesn't exist
            if (!productsData[categoryName]) {
                productsData[categoryName] = [];
            }
            const existingProductUrls = new Set(productsData[categoryName]);

            let currentPage = (i === resumeCategoryIndex) ? initialPageToResumeFrom : 1;
            let hasNextPage = true;

            // Navigate to the category URL with retry logic
            try {
                await retryOperation(async () => {
                    await page.goto(categoryUrl, { waitUntil: 'networkidle2' });
                    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for 5 seconds for page to fully render
                }, 3, 5000); // 3 retries, 5 sec delay
            } catch (error) {
                console.error(`Failed to navigate to category URL '${categoryUrl}' after multiple retries. Skipping this category. Error: ${error.message}`);
                continue; // Skip to the next category
            }


            // If resuming from a page other than 1, click "Next" buttons to reach that page
            if (currentPage > 1) {
                console.log(`Skipping to page ${currentPage} by clicking 'Next >' ${currentPage - 1} times...`);
                for (let j = 1; j < currentPage; j++) {
                    const nextButton = await page.$('input[type="submit"][value="Next >"], input[type="submit"][value="Next &gt;"]');
                    if (nextButton) {
                        console.log(`Clicking "Next >" to reach page ${j + 1}...`);
                        try {
                            await retryOperation(async () => {
                                await Promise.all([
                                    nextButton.click(),
                                    page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 60000 })
                                ]);
                            }, 3, 5000); // 3 retries, 5 sec delay for next page navigation
                            await new Promise(resolve => setTimeout(resolve, 2000)); // Small delay after navigation
                        } catch (retryError) {
                            console.error(`Failed to navigate to page ${j + 1} after retries: ${retryError.message}. Assuming end of pages for this category or persistent issue.`);
                            hasNextPage = false; // Stop pagination for this category
                            break; // Exit the loop for skipping pages
                        }
                    } else {
                        console.warn(`Could not find 'Next >' button while trying to skip to page ${currentPage}. Starting from current page.`);
                        currentPage = j; // Adjust current page to where we actually are
                        break;
                    }
                }
            }

            // Pagination loop starts from the determined currentPage
            while (hasNextPage) {
                console.log(`Scraping page ${currentPage} for ${categoryName}...`);
                try {
                    await page.waitForSelector('.product-grid', { visible: true, timeout: 30000 });
                } catch (selectorError) {
                    console.warn(`Product grid not found on page ${currentPage} for ${categoryName}. This might be the end of products or an issue with the page.`, selectorError.message);
                    hasNextPage = false;
                    continue;
                }

                const pageProductUrls = await page.evaluate(() => {
                    const productUrls = [];
                    const productTiles = document.querySelectorAll('.product-tile-content[data-click-href]');
                    productTiles.forEach(tile => {
                        const href = tile.getAttribute('data-click-href');
                        if (href) {
                            productUrls.push(href);
                        }
                    });
                    return productUrls;
                });

                let newUrlsAdded = 0;
                pageProductUrls.forEach(url => {
                    if (!existingProductUrls.has(url)) {
                        productsData[categoryName].push(url);
                        existingProductUrls.add(url);
                        newUrlsAdded++;
                    }
                });
                console.log(`Found ${pageProductUrls.length} products on page ${currentPage}. Added ${newUrlsAdded} new URLs.`);

                saveJsonFile(productsFilePath, productsData);
                console.log('Product URLs saved to benco_products_urls.json');

                // Logic to break if no new URLs were added (and products were found on page)
                if (newUrlsAdded === 0 && pageProductUrls.length > 0) {
                    console.log('No new products added on this page. Assuming end of unique products for this category.');
                    hasNextPage = false;
                    continue;
                }

                const nextButton = await page.$('input[type="submit"][value="Next >"], input[type="submit"][value="Next &gt;"]');

                if (nextButton) {
                    console.log('Clicking "Next >" button...');
                    try {
                        await retryOperation(async () => {
                            await Promise.all([
                                nextButton.click(),
                                page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 60000 })
                            ]);
                        }, 3, 5000); // 3 retries, 5 sec delay for next page navigation
                        currentPage++;
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    } catch (retryError) {
                        console.error(`Failed to navigate to the next page after retries: ${retryError.message}. Assuming end of category pages or persistent issue.`);
                        hasNextPage = false; // Stop pagination for this category
                    }
                } else {
                    console.log('No "Next >" button found. End of category pages.');
                    hasNextPage = false;
                }
            }

            // Update overall progress only when a category is fully completed
            progressData.last_overall_category_url = categoryUrl;
            saveJsonFile(progressFilePath, progressData);
            console.log(`Overall progress saved: Finished category ${categoryName}`);
        }

        console.log('\n--- Scraping process completed for all categories ---');

    } catch (error) {
        console.error('An unhandled error occurred during scraping:', error);
    } finally {
        if (browser) {
            await browser.close();
            console.log('Browser closed.');
        }
        rl.close();
    }
}

scrapeBencoProducts();
