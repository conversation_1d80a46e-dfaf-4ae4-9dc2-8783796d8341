// Import the Node.js file system module for reading and writing files.
const fs = require('fs');
const path = require('path');

// Define input and output file names.
const inputFileName = 'benco.json';
const outputProcessedFileName = 'benco_p.json';
const outputNullMfrFileName = 'benco_null.json';

// Define the path to the input file.
const inputFilePath = path.join(__dirname, inputFileName);

// --- Main processing function ---
async function processBencoData() {
    console.log(`Starting data processing for '${inputFileName}'...`);

    let rawData;
    try {
        // Read the benco.json file asynchronously.
        rawData = await fs.promises.readFile(inputFilePath, 'utf8');
    } catch (error) {
        // Handle file reading errors.
        console.error(`Error reading '${inputFileName}':`, error.message);
        console.error("Please ensure 'benco.json' exists in the same directory as the script.");
        return; // Exit if the file cannot be read.
    }

    let bencoData;
    try {
        // Parse the JSON data.
        bencoData = JSON.parse(rawData);
        // Ensure the parsed data is an array.
        if (!Array.isArray(bencoData)) {
            throw new Error("Input JSON is not an array of objects.");
        }
    } catch (error) {
        // Handle JSON parsing errors.
        console.error(`Error parsing JSON from '${inputFileName}':`, error.message);
        return; // Exit if JSON is invalid.
    }

    // Initialize arrays to store processed data and null MFR data.
    const bencoProcessed = [];
    const bencoNullMfr = [];

    // Initialize counters for various statistics.
    let totalObjects = bencoData.length;
    let mfrPresentCount = 0;
    let mfrNullCount = 0;

    // Initialize an object to count null values for specific properties in benco_p.json.
    const nullPropertyCounts = {
        mfr: 0,
        name: 0,
        url: 0,
        manufacturer: 0, // Renamed 'brand'
        maincat: 0
    };

    // Iterate over each object in the input data.
    bencoData.forEach(item => {
        // Check if the 'mfr' property is null.
        if (item.mfr === null) {
            mfrNullCount++;
            bencoNullMfr.push(item); // Add to the null MFR list.
        } else {
            mfrPresentCount++;
            // Create a new object with the specified properties and order.
            const processedItem = {
                mfr: item.mfr,
                name: item.name,
                url: item.url,
                manufacturer: item.brand, // Rename 'brand' to 'manufacturer'.
                maincat: item.maincat
            };

            // Count null values for each property in the processed item.
            for (const prop in nullPropertyCounts) {
                // Special handling for 'manufacturer' which was 'brand' in original.
                // We check the value in the *newly created* processedItem.
                if (processedItem[prop] === null) {
                    nullPropertyCounts[prop]++;
                }
            }
            bencoProcessed.push(processedItem); // Add to the processed list.
        }
    });

    try {
        // Write the processed data to benco_p.json.
        await fs.promises.writeFile(outputProcessedFileName, JSON.stringify(bencoProcessed, null, 2), 'utf8');
        console.log(`Successfully wrote ${bencoProcessed.length} objects to '${outputProcessedFileName}'`);

        // Write the null MFR data to benco_null.json.
        await fs.promises.writeFile(outputNullMfrFileName, JSON.stringify(bencoNullMfr, null, 2), 'utf8');
        console.log(`Successfully wrote ${bencoNullMfr.length} objects to '${outputNullMfrFileName}'`);

        // Log the summary statistics.
        console.log('\n--- Processing Summary ---');
        console.log(`Total objects processed: ${totalObjects}`);
        console.log(`Objects with 'mfr' value: ${mfrPresentCount}`);
        console.log(`Objects with null 'mfr' value: ${mfrNullCount}`);

        console.log('\n--- Null Value Counts in benco_p.json (for extracted properties) ---');
        for (const prop in nullPropertyCounts) {
            console.log(`'${prop}': ${nullPropertyCounts[prop]} null values`);
        }

    } catch (error) {
        // Handle file writing errors.
        console.error('Error writing output files:', error.message);
    }
}

// Execute the main processing function.
processBencoData();
