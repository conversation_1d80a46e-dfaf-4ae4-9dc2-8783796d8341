// Import the Node.js file system module for file operations
const fs = require('fs').promises;
const path = require('path');

async function consolidateDarbyDataProcessing() {
    // --- Configuration ---
    const inputFileName = 'darby.json'; // Original input file
    const outputUpdatedFileName = 'darby_updated.json'; // Successfully processed items
    const outputNullFileName = 'darby_null.json'; // Items where 'mfr' remains null

    // List of specific product names to enable detailed logging for.
    // Only products whose 'name' property matches one of these will have verbose logs.
    const TARGET_PRODUCT_NAMES_FOR_DEBUG = new Set([
        "A1, Mini-Syringe, 666350",
        "A2, Mini-Syringe, 666320",
        "A3.5, Mini-Syringe, 666330",
        "B1, Mini-Syringe, 666340",
        "Bleach White, Mini-Syringe, 666345",
        "4:1/10:1, Plunger, 77591",
        "50 ml, N69AC, A2"
    ]);

    // Construct full file paths
    const inputFilePath = path.join(__dirname, inputFileName);
    const outputUpdatedFilePath = path.join(__dirname, outputUpdatedFileName);
    const outputNullFilePath = path.join(__dirname, outputNullFileName);

    // --- Data Storage ---
    let products = []; // Array to hold products read from the input file
    let successfullyProcessedProducts = []; // Array for products where 'mfr' is successfully extracted
    let stillNullProducts = []; // Array for products where 'mfr' remains null
    let productNamesWithNullMfrExamples = []; // To log examples of products that remain unprocessed
    let darbyDentalMfrCount = 0; // Counter for products with "Darby Dental Supply" manufacturer

    // Define a set of excluded units (case-insensitive)
    // This list is comprehensive and covers various weight, volume, and count units.
    const EXCLUDED_UNITS = new Set([
        'mg', 'ml', 'g', 'kg', 'oz', 'lb', 'fl oz', 'ea', 'ct', 'unit', 'pc', 'box', 'pk', 'kit', 'set',
        'vial', 'cap', 'tab', 'amp', 'syringe', 'strip', 'disc', 'roll', 'sheet', 'pack', 'tube',
        'bottle', 'can', 'jar', 'bag', 'case', 'dozen', 'gross', 'ream', 'bundle', 'spool', 'coil',
        'reel', 'carton', 'pallet', 'drum', 'tank', 'barrel', 'bushel', 'peck', 'quart', 'pint',
        'cup', 'gallon', 'liter', 'meter', 'foot', 'inch', 'yard', 'mile', 'cm', 'mm', 'km',
        'sq ft', 'sq m', 'cu ft', 'cu m', 'l', // 'l' for liter, 'g' for gram are common single-letter units
        'ml', 'ul', 'cl', 'dl', 'kl', // milliliters, microliters, centiliters, deciliters, kiloliters
        'mcg', 'ng', 'pg', // micrograms, nanograms, picograms
        'mm2', 'cm2', 'm2', 'km2', // area units
        'mm3', 'cm3', 'm3', 'km3', // volume units
        'lb', 'oz', // pounds, ounces
        'doz', // dozen
        'pcs', // pieces
        'qty', // quantity
        'vol', // volume
        'wt', // weight
        'size', // size
        'length', // length
        'width', // width
        'height', // height
        'depth', // depth
        'dia', // diameter
        'rad', // radius
        'circ', // circumference
        'area', // area
        'vol', // volume
        'temp', // temperature
        'pres', // pressure
        'conc', // concentration
        'dens', // density
        'flow', // flow rate
        'speed', // speed
        'time', // time
        'freq', // frequency
        'amp', // amperage
        'volt', // voltage
        'ohm', // resistance
        'watt', // power
        'joule', // energy
        'cal', // calorie
        'btu', // BTU
        'psi', // PSI
        'bar', // bar
        'pa', // pascal
        'hz', // hertz
        'db', // decibel
        'rpm', // RPM
        'gpm', // GPM
        'cfm', // CFM
        'cfs', // CFS
        'bps', // BPS
        'baud', // baud
        'byte', 'kb', 'mb', 'gb', 'tb', // data units
        'bit', 'kbit', 'mbit', 'gbit', 'tbit', // data units
        'px', 'pt', 'em', 'rem', 'vw', 'vh', // CSS units, might appear in product names
        'deg', 'rad', // degrees, radians
        'rpm', 'rps', // revolutions per minute/second
        'kwh', // kilowatt-hour
        'btu', // British Thermal Unit
        'cal', // calorie
        'kcal', // kilocalorie
        'cc', // cubic centimeter
        'c.c.', // cubic centimeter
        'cu in', // cubic inch
        'cu ft', // cubic foot
        'cu yd', // cubic yard
        'sq in', // square inch
        'sq ft', // square foot
        'sq yd', // square yard
        'sq mi', // square mile
        'ac', // acre
        'ha', // hectare
        'gal', // gallon
        'qt', // quart
        'pt', // pint
        'cup', // cup
        'tbsp', // tablespoon
        'tsp', // teaspoon
        'fl dr', // fluid dram
        'dr', // dram
        'gr', // grain
        'oz t', // troy ounce
        'lb t', // troy pound
        'st', // stone
        'ton', // ton
        'mt', // metric ton
        'ctn', // carton
        'bag', // bag
        'roll', // roll
        'sheet', // sheet
        'pad', // pad
        'pkt', // packet
        'bx', // box
        'dz', // dozen
        'pr', // pair
        'set', // set
        'kit', // kit
        'case', // case
        'bundle', // bundle
        'pack', // pack
        'tube', // tube
        'bottle', // bottle
        'can', // can
        'jar', // jar
        'vial', // vial
        'amp', // ampule
        'caps', // capsules
        'tabs', // tablets
        'syringes', // syringes
        'strips', // strips
        'discs', // discs
        'reels', // reels
        'spools', // spools
        'coils', // coils
        'drums', // drums
        'tanks', // tanks
        'barrels', // barrels
        'bushels', // bushels
        'pecks', // pecks
        'quarts', // quarts
        'pints', // pints
        'cups', // cups
        'gallons', // gallons
        'liters', // liters
        'meters', // meters
        'feet', // feet
        'inches', // inches
        'yards', // yards
        'miles', // miles
        'cms', 'mms', 'kms', // plural versions
        'sq fts', 'sq ms', 'cu fts', 'cu ms', // plural versions
        'lbs', 'ozs', // plural versions
        'dozs', // plural versions
        'pcss', // plural pieces
        'vols', // plural volumes
        'wts', // plural weights
        'sizes', // plural sizes
        'lengths', // plural lengths
        'widths', // plural widths
        'heights', // plural heights
        'depths', // plural depths
        'diameters', // plural diameters
        'radii', // plural radii
        'circs', // plural circumferences
        'areas', // plural areas
        'temps', // plural temperatures
        'press', // plural pressures
        'concs', // plural concentrations
        'denss', // plural densities
        'flows', // plural flow rates
        'speeds', // plural speeds
        'times', // plural times
        'freqs', // plural frequencies
        'amps', // plural amperages
        'volts', // plural voltages
        'ohms', // plural resistances
        'watts', // plural powers
        'joules', // plural energies
        'cals', // plural calories
        'btus', // plural BTUs
        'psis', // plural PSIs
        'bars', // plural bars
        'pa', // pascal
        'hz', // hertz
        'db', // decibel
        'rpm', // RPM
        'gpm', // GPM
        'cfm', // CFM
        'cfs', // CFS
        'bps', // BPS
        'baud', // baud
        'byte', 'kb', 'mb', 'gb', 'tb', // data units
        'bit', 'kbit', 'mbit', 'gbit', 'tbit', // data units
        'px', 'pt', 'em', 'rem', 'vw', 'vh', // CSS units, might appear in product names
        'deg', 'rad', // degrees, radians
        'rpm', 'rps', // revolutions per minute/second
        'kwh', // kilowatt-hour
        'btu', // British Thermal Unit
        'cal', // calorie
        'kcal', // kilocalorie
        'cc', // cubic centimeter
        'c.c.', // cubic centimeter
        'cu in', // cubic inch
        'cu ft', // cubic foot
        'cu yd', // cubic yard
        'sq in', // square inch
        'sq ft', // square foot
        'sq yd', // square yard
        'sq mi', // square mile
        'ac', // acre
        'ha', // hectare
        'gal', // gallon
        'qt', // quart
        'pt', // pint
        'cup', // cup
        'tbsp', // tablespoon
        'tsp', // teaspoon
        'fl dr', // fluid dram
        'dr', // dram
        'gr', // grain
        'oz t', // troy ounce
        'lb t', // troy pound
        'st', // stone
        'ton', // ton
        'mt', // metric ton
        'ctn', // carton
        'bag', // bag
        'roll', // roll
        'sheet', // sheet
        'pad', // pad
        'pkt', // packet
        'bx', // box
        'dz', // dozen
        'pr', // pair
        'set', // set
        'kit', // kit
        'case', // case
        'bundle', // bundle
        'pack', // pack
        'tube', // tube
        'bottle', // bottle
        'can', // can
        'jar', // jar
        'vial', // vial
        'amp', // ampule
        'caps', // capsules
        'tabs', // tablets
        'syringes', // syringes
        'strips', // strips
        'discs', // discs
        'reels', // reels
        'spools', // spools
        'coils', // coils
        'drums', // drums
        'tanks', // tanks
        'barrels', // barrels
        'bushels', // bushels
        'pecks', // pecks
        'quarts', // quarts
        'pints', // pints
        'cups', // cups
        'gallons', // gallons
        'liters', // liters
        'meters', // meters
        'feet', // feet
        'inches', // inches
        'yards', // yards
        'miles', // miles
        'cms', 'mms', 'kms', // plural versions
        'sq fts', 'sq ms', 'cu fts', 'cu ms', // plural versions
        'lbs', 'ozs', // plural versions
        'dozs', // plural versions
        'pcss', // plural pieces
        'vols', // plural volumes
        'wts', // plural weights
        'sizes', // plural sizes
        'lengths', // plural lengths
        'widths', // plural widths
        'heights', // plural heights
        'depths', // plural depths
        'diameters', // plural diameters
        'radii', // plural radii
        'circs', // plural circumferences
        'areas', // plural areas
        'temps', // plural temperatures
        'press', // plural pressures
        'concs', // plural concentrations
        'denss', // plural densities
        'flows', // plural flow rates
        'speeds', // plural speeds
        'times', // plural times
        'freqs', // plural frequencies
        'amps', // plural amperages
        'volts', // plural voltages
        'ohms', // plural resistances
        'watts', // plural powers
        'joules', // plural energies
        'cals', // plural calories
        'btus', // plural BTUs
        'psis', // plural PSIs
        'bars', // plural bars
        'pas', // plural pascals
        'hzs', // plural hertz
        'dbs', // plural decibels
        'rpms', // plural RPMs
        'gpms', // plural GPMs
        'cfms', // plural CFMs
        'cfss', // plural CFSs
        'bpss', // plural BPSs
        'bauds', // plural bauds
        'bytes', 'kbs', 'mb', 'gb', 'tb', // data units (corrected 'mb' from 'mbs')
        'bit', 'kbit', 'mbit', 'gbit', 'tbit', // data units
        'pxs', 'pts', 'ems', 'rems', 'vws', 'vhs', // CSS units, might appear in product names
        'degs', 'rads', // degrees, radians
        'rpm', 'rps', // revolutions per minute/second
        'kwhs', // kilowatt-hour
        'ccs', // cubic centimeters
        'cu ins', 'cu fts', 'cu yds', // cubic inches/feet/yards
        'sq ins', 'sq fts', 'sq yds', 'sq mis', // square inches/feet/yards
        'acs', 'has', // acres/hectares
        'gals', 'qts', 'pts', 'cups', 'tbsps', 'tsps', 'fl drs', 'drs', 'grs', // liquid/weight units
        'oz ts', 'lb ts', 'sts', 'tons', 'mts', // troy/weight units
        'ctns', 'bags', 'rolls', 'sheets', 'pads', 'pkts', 'bxs', 'dzs', 'prs', // packaging/count units
    ]);

    try {
        // 1. Read the darby.json file
        console.log(`Reading data from ${inputFileName}...`);
        const data = await fs.readFile(inputFilePath, 'utf8');

        // 2. Parse its content as a JSON array
        products = JSON.parse(data);
        console.log(`Successfully read ${products.length} products from ${inputFileName}.`);

        // Regex to validate a segment as a potential MFR pattern:
        // - `^`: Start of the string (after trimming the segment).
        // - `([A-Z0-9][a-zA-Z0-9-]{3,})`: Capturing group for the pattern.
        //   - `[A-Z0-9]`: Starts with uppercase letter or number.
        //   - `[a-zA-Z0-9-]{3,}`: Followed by 3+ alphanumeric or hyphen characters (total length >= 4).
        // - `$`: End of the string (after trimming the segment).
        const segmentPatternRegex = /^([A-Z0-9][a-zA-Z0-9-]{3,})$/;

        // Regex to check if a string contains at least one digit
        const containsDigitRegex = /\d/;

        // 3. Iterate through each product in the array
        for (const product of products) {
            let mfrValue = null; // Initialize mfrValue for the current product
            const enableProductDebug = TARGET_PRODUCT_NAMES_FOR_DEBUG.has(product.name);

            if (enableProductDebug) {
                console.log(`\n--- Processing Product: ${product.name} (Product Number: ${product.productnumber}) ---`);
            }

            // Rule 1: Special handling for "Darby Dental Supply" manufacturer
            // If manufacturer is "Darby Dental Supply", set mfr to cleaned productnumber.
            // This rule takes precedence over all other regex-based extraction.
            if (typeof product.manufacturer === 'string' && product.manufacturer === "Darby Dental Supply") {
                if (typeof product.productnumber === 'string') {
                    mfrValue = product.productnumber.replace(/-/g, '');
                    darbyDentalMfrCount++;
                    if (enableProductDebug) {
                        console.log(`  [Rule: Darby Dental Supply] MFR set to cleaned product number: ${mfrValue}`);
                    }
                } else {
                    if (enableProductDebug) {
                        console.log(`  [Rule: Darby Dental Supply] Product number not found or not string. MFR remains null.`);
                    }
                }
            } else {
                // Apply general pattern extraction and filtering for other manufacturers
                const cleanedProductNumber = typeof product.productnumber === 'string'
                    ? product.productnumber.replace(/-/g, '')
                    : null;

                if (enableProductDebug) {
                    console.log(`  Cleaned Product Number: ${cleanedProductNumber}`);
                }

                // Ensure the 'name' property exists and is a string
                if (typeof product.name === 'string') {
                    // Split the name by commas and process segments from right to left (latest first)
                    const nameSegments = product.name.split(',').map(s => s.trim());

                    if (enableProductDebug) {
                        console.log(`  Name segments (right to left): [${[...nameSegments].reverse().join(', ')}]`);
                    }

                    // Iterate segments in reverse order to find the "latest" valid pattern
                    for (let i = nameSegments.length - 1; i >= 0; i--) {
                        const segment = nameSegments[i];
                        const match = segment.match(segmentPatternRegex);

                        if (enableProductDebug) {
                            console.log(`    Evaluating segment: "${segment}"`);
                        }

                        if (match && match[1]) {
                            const extractedPattern = match[1];
                            const lowerCasePattern = extractedPattern.toLowerCase();

                            if (enableProductDebug) {
                                console.log(`      Potential pattern extracted from segment: "${extractedPattern}"`);
                            }

                            // Rule: The pattern should not match "productnumber" property value after cleaning it from dashes.
                            if (cleanedProductNumber && extractedPattern === cleanedProductNumber) {
                                if (enableProductDebug) {
                                    console.log(`        - Filtered: Matches cleaned product number (${cleanedProductNumber})`);
                                }
                                continue; // Skip this match
                            }

                            // Rule: It should contain at least one number. Can not be all letters.
                            if (!containsDigitRegex.test(extractedPattern)) {
                                if (enableProductDebug) {
                                    console.log(`        - Filtered: Does not contain any digits`);
                                }
                                continue; // Skip this match
                            }

                            // Rule: It should not contain a weight or volume or count units.
                            let isUnit = false;
                            for (const unit of EXCLUDED_UNITS) {
                                if (lowerCasePattern.endsWith(unit)) {
                                    const charBeforeUnitIndex = lowerCasePattern.length - unit.length - 1;
                                    if (lowerCasePattern === unit || (charBeforeUnitIndex >= 0 && /\d/.test(lowerCasePattern[charBeforeUnitIndex]))) {
                                        isUnit = true;
                                        break;
                                    }
                                }
                            }
                            if (isUnit) {
                                if (enableProductDebug) {
                                    console.log(`        - Filtered: Identified as a unit (${extractedPattern})`);
                                }
                                continue; // Skip this match
                            }

                            // If all rules pass, this is our valid MFR (since we're iterating from right to left)
                            mfrValue = extractedPattern;
                            if (enableProductDebug) {
                                console.log(`        - Passed all filters. Valid MFR found: ${mfrValue}`);
                            }
                            break; // Stop searching for patterns in this product's name
                        } else {
                            if (enableProductDebug) {
                                console.log(`      - Segment "${segment}" does not match basic pattern regex.`);
                            }
                        }
                    }
                } else {
                    if (enableProductDebug) {
                        console.log(`  'name' property not found or not a string.`);
                    }
                }
            }

            // Assign the determined mfrValue to the product
            product.mfr = mfrValue;

            // Separate products based on whether 'mfrValue' is null or not
            if (mfrValue === null) {
                stillNullProducts.push(product);
                // Collect examples of product names that remain null, up to 20 for logging.
                if (productNamesWithNullMfrExamples.length < 20) {
                    productNamesWithNullMfrExamples.push(product.name);
                }
            } else {
                successfullyProcessedProducts.push(product);
            }
        }

        // 4. Save the results to their respective JSON files
        console.log(`\nSaving successfully processed products to ${outputUpdatedFileName}...`);
        await fs.writeFile(outputUpdatedFilePath, JSON.stringify(successfullyProcessedProducts, null, 2), 'utf8');
        console.log(`Successfully saved ${successfullyProcessedProducts.length} products to ${outputUpdatedFileName}.`);

        console.log(`Saving still null 'mfr' products to ${outputNullFileName}...`);
        await fs.writeFile(outputNullFilePath, JSON.stringify(stillNullProducts, null, 2), 'utf8');
        console.log(`Successfully saved ${stillNullProducts.length} products to ${outputNullFileName}.`);

        // 5. Log the summary and examples
        console.log('\n--- Consolidated Processing Summary ---');
        console.log(`Total products initially read from ${inputFileName}: ${products.length}`);
        console.log(`Products processed with 'Darby Dental Supply' rule: ${darbyDentalMfrCount}`);
        console.log(`Products successfully processed with 'mfr' (saved to ${outputUpdatedFileName}): ${successfullyProcessedProducts.length}`);
        console.log(`Products still with null 'mfr' (saved to ${outputNullFileName}): ${stillNullProducts.length}`);

        if (productNamesWithNullMfrExamples.length > 0) {
            console.log('\nExamples of product names where pattern was NOT found (mfr is null):');
            productNamesWithNullMfrExamples.forEach((name, index) => console.log(`${index + 1}. ${name}`));
            if (stillNullProducts.length > 20) {
                console.log(`... (and ${stillNullProducts.length - 20} more)`);
            }
        } else {
            console.log('\nNo product names found where the pattern was NOT found.');
        }

    } catch (error) {
        console.error(`\nAn error occurred during processing: ${error.message}`);
        if (error.code === 'ENOENT') {
            console.error(`Error: The file '${inputFileName}' was not found. Please make sure it's in the same directory as the script.`);
        } else {
            console.error(`An unexpected error occurred: ${error.message}`);
        }
    }
}

// Execute the function
consolidateDarbyDataProcessing();
