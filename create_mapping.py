#!/usr/bin/env python3
"""
Script to create standardized manufacturer mapping from uniquebrand.json
Groups similar manufacturer names and maps them to standardized representations.
"""

import json
import re
from collections import defaultdict
from pathlib import Path

def normalize_for_comparison(name):
    """Normalize manufacturer name for comparison purposes."""
    # Convert to lowercase
    name = name.lower()
    # Remove common suffixes and prefixes
    name = re.sub(r'\b(inc\.?|corp\.?|corporation|company|co\.?|ltd\.?|llc|llp)\b', '', name)
    name = re.sub(r'\b(the|a|an)\b', '', name)
    # Remove punctuation and extra spaces
    name = re.sub(r'[^\w\s]', ' ', name)
    name = re.sub(r'\s+', ' ', name).strip()
    return name

def create_standardized_name(name):
    """Create a standardized name following the pattern from ubrands.json."""
    # Start with the original name
    std_name = name.lower()

    # Remove common business suffixes
    std_name = re.sub(r'\b(inc\.?|corp\.?|corporation|company|co\.?|ltd\.?|llc|llp|international|intl\.?|usa|america|american)\b', '', std_name)

    # Remove "the", "a", "an"
    std_name = re.sub(r'\b(the|a|an)\b', '', std_name)

    # Remove punctuation except hyphens and spaces
    std_name = re.sub(r'[^\w\s\-]', '', std_name)

    # Clean up spaces
    std_name = re.sub(r'\s+', '', std_name).strip()

    # Handle special cases for numbers at the beginning
    if std_name.startswith('3m'):
        return '3msolventum'  # Following the pattern from ubrands.json
    elif std_name.startswith('3d'):
        return '3ddental'
    elif std_name.startswith('3shape'):
        return '3shape'

    # If name is too long, try to shorten it intelligently
    if len(std_name) > 15:
        words = std_name.split()
        if len(words) > 1:
            # Take first word if it's meaningful, otherwise first two
            if len(words[0]) >= 4:
                std_name = words[0]
            else:
                std_name = ''.join(words[:2])

    return std_name

def find_similar_names(names):
    """Group similar manufacturer names together."""
    groups = []
    used = set()

    for name in names:
        if name in used:
            continue

        # Start a new group with this name
        group = [name]
        used.add(name)
        normalized_base = normalize_for_comparison(name)

        # Find similar names
        for other_name in names:
            if other_name in used:
                continue

            normalized_other = normalize_for_comparison(other_name)

            # Check for similarity
            if are_similar(normalized_base, normalized_other, name, other_name):
                group.append(other_name)
                used.add(other_name)

        groups.append(group)

    return groups

def are_similar(norm1, norm2, orig1, orig2):
    """Determine if two normalized names are similar enough to be grouped."""
    # Exact match after normalization
    if norm1 == norm2:
        return True

    # One is a substring of the other (with some length requirements)
    if len(norm1) >= 4 and len(norm2) >= 4:
        if norm1 in norm2 or norm2 in norm1:
            return True

    # Check for common patterns
    words1 = set(norm1.split())
    words2 = set(norm2.split())

    # If they share significant words
    if words1 and words2:
        common_words = words1.intersection(words2)
        if common_words:
            # At least one significant common word
            for word in common_words:
                if len(word) >= 4:  # Significant word length
                    return True

    # Check for abbreviation patterns
    if is_abbreviation_match(orig1, orig2):
        return True

    return False

def is_abbreviation_match(name1, name2):
    """Check if one name might be an abbreviation of another."""
    # Simple abbreviation check
    name1_clean = re.sub(r'[^\w]', '', name1.lower())
    name2_clean = re.sub(r'[^\w]', '', name2.lower())

    # Check if shorter name could be abbreviation of longer
    if len(name1_clean) < len(name2_clean):
        short, long = name1_clean, name2_clean
    else:
        short, long = name2_clean, name1_clean

    if len(short) <= 6 and len(long) >= len(short) * 2:
        # Check if short could be initials of long
        words = re.findall(r'\b\w', long)
        if len(words) >= len(short):
            initials = ''.join(words[:len(short)])
            if initials == short:
                return True

    return False

def load_existing_mapping():
    """Load existing mapping from prototype/ubrands.json for reference."""
    try:
        with open('prototype/ubrands.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Warning: prototype/ubrands.json not found, proceeding without reference")
        return {}

def choose_best_representative(group, existing_mapping):
    """Choose the best representative name from a group."""
    # Check if any name in the group exists in the existing mapping
    for name in group:
        if name in existing_mapping:
            return existing_mapping[name]

    # If no existing mapping, choose the shortest meaningful name
    # Prefer names without too much punctuation or weird formatting
    candidates = []
    for name in group:
        score = 0
        # Prefer shorter names
        score -= len(name) * 0.1
        # Prefer names without excessive punctuation
        punct_count = len(re.findall(r'[^\w\s]', name))
        score -= punct_count * 2
        # Prefer names that don't have weird formatting
        if name.isupper() and len(name) > 4:
            score -= 5  # Penalize all-caps long names
        # Prefer names that look like company names
        if any(word in name.lower() for word in ['dental', 'medical', 'health', 'corp', 'inc']):
            score += 2

        candidates.append((score, name))

    # Sort by score (higher is better) and take the best
    candidates.sort(reverse=True)
    best_name = candidates[0][1]

    # Create standardized version
    return create_standardized_name(best_name)

def create_manufacturer_mapping():
    """Main function to create the manufacturer mapping."""
    # Load unique manufacturer names
    try:
        with open('uniquebrand.json', 'r', encoding='utf-8') as f:
            unique_names = json.load(f)
    except FileNotFoundError:
        print("Error: uniquebrand.json not found. Please run extract_manufacturers.py first.")
        return

    print(f"Loaded {len(unique_names)} unique manufacturer names")

    # Load existing mapping for reference
    existing_mapping = load_existing_mapping()
    print(f"Loaded {len(existing_mapping)} existing mappings for reference")

    # Group similar names
    print("Grouping similar manufacturer names...")
    groups = find_similar_names(unique_names)

    print(f"Created {len(groups)} groups from {len(unique_names)} names")

    # Create the mapping
    mapping = {}

    for i, group in enumerate(groups):
        if i % 100 == 0:
            print(f"Processing group {i+1}/{len(groups)}")

        # Choose the best standardized name for this group
        standard_name = choose_best_representative(group, existing_mapping)

        # Map all names in the group to the standard name
        for name in group:
            mapping[name] = standard_name

    # Save the mapping
    output_file = "map.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mapping, f, indent=2, ensure_ascii=False)

        print(f"\nMapping saved to: {output_file}")
        print(f"Total mappings: {len(mapping)}")
        print(f"Unique standardized names: {len(set(mapping.values()))}")

        # Show some examples
        print("\nSample mappings:")
        sample_count = 0
        for original, standardized in mapping.items():
            if sample_count >= 10:
                break
            print(f"  '{original}' -> '{standardized}'")
            sample_count += 1

    except Exception as e:
        print(f"Error saving mapping to {output_file}: {e}")

if __name__ == "__main__":
    create_manufacturer_mapping()