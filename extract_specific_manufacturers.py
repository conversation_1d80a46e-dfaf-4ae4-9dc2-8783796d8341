#!/usr/bin/env python3
"""
Script to extract unique manufacturer names from specific JSON files:
darby_up.json, tdsc_up.json, midwest_up.json, safco_up.json, net32_up.json
"""

import json
import os
from pathlib import Path

def extract_specific_manufacturers():
    """Extract unique manufacturer names from the 5 specified JSON files."""
    
    # Specific files to process
    target_files = [
        "darby_up.json",
        "tdsc_up.json", 
        "midwest_up.json",
        "safco_up.json",
        "net32_up.json"
    ]
    
    # Set to store unique manufacturer names
    unique_manufacturers = set()
    
    # Path to dbapi folder
    dbapi_path = Path("dbapi")
    
    # Check if dbapi folder exists
    if not dbapi_path.exists():
        print(f"Error: {dbapi_path} folder not found!")
        return
    
    print(f"Processing {len(target_files)} specific files:")
    for file in target_files:
        print(f"  - {file}")
    
    # Process each specified JSON file
    total_products = 0
    processed_files = 0
    
    for filename in target_files:
        json_file = dbapi_path / filename
        
        if not json_file.exists():
            print(f"Warning: {filename} not found in {dbapi_path}")
            continue
            
        try:
            print(f"\nProcessing {filename}...")
            
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Ensure data is a list
            if not isinstance(data, list):
                print(f"Warning: {filename} does not contain a list of products")
                continue
            
            file_products = 0
            file_manufacturers = set()
            
            # Extract manufacturer names from each product
            for product in data:
                if isinstance(product, dict) and 'manufacturer' in product:
                    manufacturer = product['manufacturer']
                    if manufacturer and isinstance(manufacturer, str):
                        # Clean up the manufacturer name (strip whitespace)
                        manufacturer = manufacturer.strip()
                        if manufacturer:  # Only add non-empty strings
                            unique_manufacturers.add(manufacturer)
                            file_manufacturers.add(manufacturer)
                            file_products += 1
            
            print(f"  - Products processed: {file_products}")
            print(f"  - Unique manufacturers in this file: {len(file_manufacturers)}")
            
            total_products += file_products
            processed_files += 1
            
        except json.JSONDecodeError as e:
            print(f"Error parsing {filename}: {e}")
        except Exception as e:
            print(f"Error processing {filename}: {e}")
    
    print(f"\n=== SUMMARY ===")
    print(f"Files processed: {processed_files}/{len(target_files)}")
    print(f"Total products processed: {total_products}")
    print(f"Total unique manufacturers found: {len(unique_manufacturers)}")
    
    # Convert set to sorted list for consistent output
    unique_manufacturers_list = sorted(list(unique_manufacturers))
    
    # Save to specific_manufacturers.json
    output_file = "specific_manufacturers.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(unique_manufacturers_list, f, indent=2, ensure_ascii=False)
        
        print(f"\nUnique manufacturer names saved to: {output_file}")
        print(f"First 10 manufacturers (alphabetically):")
        for i, manufacturer in enumerate(unique_manufacturers_list[:10]):
            print(f"  {i+1}. {manufacturer}")
        
        if len(unique_manufacturers_list) > 10:
            print(f"  ... and {len(unique_manufacturers_list) - 10} more")
            
    except Exception as e:
        print(f"Error saving to {output_file}: {e}")

if __name__ == "__main__":
    extract_specific_manufacturers()
