const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// --- Configuration ---
const BASE_URL = 'https://www.darbydental.com';
const USER_DATA_DIR = path.join(__dirname, 'puppeteer_user_data'); // Directory to store session data
const CATEGORIES_FILE = 'cats.json'; // File containing category names and URLs
const PRODUCTS_OUTPUT_FILE = 'producturls.json'; // Output file for scraped product URLs
const PROGRESS_FILE = 'progress.json'; // File to save scraping progress
const PRODUCTS_PER_PAGE_VALUE = '225'; // Value for the "products per page" dropdown

// --- Global Data Storage ---
let allProductUrls = {}; // Stores scraped product URLs: { "Category Name": ["url1", "url2", ...], ... }
// Progress now tracks the index of the LAST CATEGORY FULLY COMPLETED and saved
let progress = {
    lastCompletedCategoryIndex: -1 // Initialize to -1 so it starts from index 0 if no progress
};

// --- Helper Functions ---

/**
 * Loads data from a JSON file.
 * @param {string} filePath - Path to the JSON file.
 * @returns {object|null} Parsed JSON data or null if file doesn't exist/is invalid.
 */
function loadJson(filePath) {
    if (fs.existsSync(filePath)) {
        try {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error(`Error reading or parsing ${filePath}:`, error);
            return null;
        }
    }
    return null;
}

/**
 * Saves data to a JSON file.
 * @param {string} filePath - Path to the JSON file.
 * @param {object} data - Data to save.
 */
function saveJson(filePath, data) {
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`Data saved to ${filePath}`);
    } catch (error) {
        console.error(`Error saving to ${filePath}:`, error);
    }
}

/**
 * Parses the result count string (e.g., "0-225 of 566 results")
 * @param {string} resultCountString - The string from #MainContent_resultCount
 * @returns {{start: number, end: number, total: number}|null} Parsed numbers or null if invalid.
 */
function parseResultCount(resultCountString) {
    const match = resultCountString.match(/(\d+)-(\d+) of (\d+) results/);
    if (match && match.length === 4) {
        return {
            start: parseInt(match[1], 10),
            end: parseInt(match[2], 10),
            total: parseInt(match[3], 10)
        };
    }
    return null;
}

// --- Main Scraping Logic ---

async function scrapeProducts() {
    // Load existing product URLs and progress
    const existingProductUrls = loadJson(PRODUCTS_OUTPUT_FILE);
    if (existingProductUrls) {
        allProductUrls = existingProductUrls;
        console.log(`Loaded ${Object.keys(allProductUrls).length} existing categories from ${PRODUCTS_OUTPUT_FILE}`);
    }

    const loadedProgress = loadJson(PROGRESS_FILE);
    if (loadedProgress && typeof loadedProgress.lastCompletedCategoryIndex === 'number') {
        progress = { ...progress, ...loadedProgress };
        console.log(`Resuming from progress: Last completed category index ${progress.lastCompletedCategoryIndex}`);
    }

    const categories = loadJson(CATEGORIES_FILE);
    if (!categories || categories.length === 0) {
        console.error(`No categories found in ${CATEGORIES_FILE}. Please run the category scraper first.`);
        return;
    }

    // Launch browser with user data directory for session persistence
    const browser = await puppeteer.launch({
        headless: false, // Keep headless false for visual debugging
        defaultViewport: null, // Don't set a specific viewport size
        userDataDir: USER_DATA_DIR // Store cookies and local storage here
    });
    const page = await browser.newPage();

    // Listen for console messages from the browser context for debugging
    page.on('console', msg => console.log('BROWSER CONSOLE:', msg.text()));
    page.on('pageerror', err => console.error('PAGE ERROR:', err.toString()));

    try {
        // Start loop from the next category after the last completed one
        for (let i = progress.lastCompletedCategoryIndex + 1; i < categories.length; i++) {
            const category = categories[i];
            const categoryName = category.name;
            const categoryUrl = category.url;

            // Initialize product list for this category if not already present
            if (!allProductUrls[categoryName]) {
                allProductUrls[categoryName] = [];
            }

            console.log(`\n--- Scraping Category: "${categoryName}" (${categoryUrl}) ---`);

            // Reset page number and scraped count for the current category (local to this category's loop)
            let currentPageNumber = 1;
            let categoryProductsScraped = 0;

            // Navigate to the category URL
            await page.goto(categoryUrl, { waitUntil: 'networkidle2', timeout: 60000 });
            console.log(`Navigated to category page: ${categoryUrl}`);

            // Wait for the main container to ensure page content is loaded
            await page.waitForSelector('div.container', { timeout: 30000 });
            console.log('Main container found.');

            // --- Set Products Per Page to 225 ---
            console.log(`Attempting to set products per page to ${PRODUCTS_PER_PAGE_VALUE}...`);
            let productsPerPageSet = false;
            let attempts = 0;
            const MAX_ATTEMPTS = 5;

            while (!productsPerPageSet && attempts < MAX_ATTEMPTS) {
                attempts++;
                try {
                    // Select the 225 option
                    await page.select('#MainContent_selPerPage', PRODUCTS_PER_PAGE_VALUE);
                    console.log(`Selected ${PRODUCTS_PER_PAGE_VALUE} items per page. Attempt ${attempts}.`);

                    // Wait for the product container to reload or result count to update
                    // Condition now checks if end count is expected (225) OR if end count equals total (for smaller categories)
                    await page.waitForFunction(
                        (expectedCount) => {
                            const resultCountSpan = document.querySelector('#MainContent_resultCount');
                            if (resultCountSpan) {
                                const text = resultCountSpan.innerText;
                                const match = text.match(/(\d+)-(\d+) of (\d+) results/);
                                if (match) {
                                    const currentEnd = parseInt(match[2], 10);
                                    const total = parseInt(match[3], 10);
                                    // NEW LOGIC: Check if end count is expected (225) OR if it matches total products
                                    return currentEnd === expectedCount || currentEnd === total;
                                }
                            }
                            return false;
                        },
                        { timeout: 30000 },
                        parseInt(PRODUCTS_PER_PAGE_VALUE, 10) // Pass 225 to the browser function
                    );
                    console.log(`Products per page successfully set to ${PRODUCTS_PER_PAGE_VALUE}.`);
                    productsPerPageSet = true;
                } catch (error) {
                    console.warn(`Failed to set products per page (attempt ${attempts}):`, error.message);
                    if (attempts < MAX_ATTEMPTS) {
                        await new Promise(resolve => setTimeout(resolve, 2000 * attempts));                        
                        console.log('Retrying...');
                    }
                }
            }

            if (!productsPerPageSet) {
                console.error(`Failed to set products per page to ${PRODUCTS_PER_PAGE_VALUE} after ${MAX_ATTEMPTS} attempts. Skipping category.`);
                continue; // Skip to next category
            }

            // --- Pagination Loop ---
            let isLastPage = false;
            while (!isLastPage) {
                console.log(`Scraping page ${currentPageNumber} for "${categoryName}"...`);

                // Wait for the product container to be present on the current page
                await page.waitForSelector('#productContainer', { timeout: 30000 });

                // Get current result count before scraping to determine expected next page start
                const currentResultCountString = await page.$eval('#MainContent_resultCount', el => el.innerText);
                const currentResultCount = parseResultCount(currentResultCountString);

                if (!currentResultCount) {
                    console.warn('Could not parse current result count string. Skipping page scraping.');
                    isLastPage = true; // Prevent infinite loop if count parsing fails
                    continue;
                }

                console.log(`Current Result Count: ${currentResultCount.start}-${currentResultCount.end} of ${currentResultCount.total} results.`);

                // Scrape product URLs from the current page
                const currentPageProductUrls = await page.evaluate((baseURL) => {
                    const urls = [];
                    const productContainer = document.querySelector('#productContainer');
                    if (productContainer) {
                        const productLinks = productContainer.querySelectorAll('.card-body a');
                        productLinks.forEach(link => {
                            const relativeHref = link.getAttribute('href');
                            if (relativeHref) {
                                urls.push(new URL(relativeHref, baseURL).href);
                            }
                        });
                    }
                    return urls;
                }, BASE_URL);

                console.log(`Found ${currentPageProductUrls.length} products on page ${currentPageNumber}.`);
                allProductUrls[categoryName].push(...currentPageProductUrls);
                categoryProductsScraped += currentPageProductUrls.length;

                // Determine if it's the last page based on the current result count
                if (currentResultCount.end >= currentResultCount.total) {
                    isLastPage = true;
                    console.log(`Reached last page for "${categoryName}".`);
                }

                if (!isLastPage) {
                    console.log('Attempting to navigate to "Next Page"...');
                    const nextButtonSelector = '#MainContent_pagelinkNext'; // Desktop version
                    const nextButtonSelectorSm = '#MainContent_pagelinkNextSm'; // Mobile version

                    let nextHref = null;
                    try {
                        // Try to get href from desktop button first
                        nextHref = await page.evaluate((selector) => {
                            const btn = document.querySelector(selector);
                            // Ensure button exists and is not disabled (aspNetDisabled class)
                            if (btn && !btn.classList.contains('aspNetDisabled')) {
                                return btn.getAttribute('href');
                            }
                            return null;
                        }, nextButtonSelector);

                        if (!nextHref) {
                            // Fallback to mobile button if desktop is not available/enabled
                            nextHref = await page.evaluate((selector) => {
                                const btn = document.querySelector(selector);
                                if (btn && !btn.classList.contains('aspNetDisabled')) {
                                    return btn.getAttribute('href');
                                }
                                return null;
                            }, nextButtonSelectorSm);
                        }

                        if (nextHref && nextHref.startsWith('javascript:')) {
                            const jsFunctionCall = nextHref.substring('javascript:'.length);
                            console.log(`Executing JavaScript for next page: ${jsFunctionCall.substring(0, 50)}...`); // Log first 50 chars
                            await page.evaluate(jsFunctionCall);

                            // *** NEW: Wait for result count to increment ***
                            // This ensures we've truly navigated to the next page
                            const expectedNextPageStart = currentResultCount.end;
                            console.log(`Waiting for page to load with new products (expecting start count: ${expectedNextPageStart})...`);

                            await page.waitForFunction(
                                (expectedStart) => {
                                    const resultCountSpan = document.querySelector('#MainContent_resultCount');
                                    if (resultCountSpan) {
                                        const text = resultCountSpan.innerText;
                                        const match = text.match(/(\d+)-(\d+) of (\d+) results/);
                                        if (match && parseInt(match[1], 10) === expectedStart) {
                                            return true; // Condition met: start count has incremented
                                        }
                                    }
                                    return false;
                                },
                                { timeout: 45000 }, // Increased timeout for potentially longer load times
                                expectedNextPageStart
                            );
                            console.log(`Confirmed navigation to next page (start count is now ${expectedNextPageStart}).`);
                            currentPageNumber++; // Increment local page number

                        } else {
                            console.log('Next page button not found or not enabled, or href is not a JavaScript function. Assuming last page.');
                            isLastPage = true; // Break loop if next button is disabled or not found
                        }

                    } catch (error) {
                        console.error('Error navigating to next page or waiting for load:', error.message);
                        isLastPage = true; // Break loop on error to prevent infinite loop
                    }
                }

                // Progress is saved only after a full category is scraped,
                // or if the script is interrupted (handled by finally block for browser close)
            }
            console.log(`Finished scraping all pages for "${categoryName}". Total products: ${allProductUrls[categoryName].length}`);

            // Save all product URLs for this category *after* all its pages are scraped
            saveJson(PRODUCTS_OUTPUT_FILE, allProductUrls);

            // Update progress ONLY after a category is fully scraped and saved
            progress.lastCompletedCategoryIndex = i;
            saveJson(PROGRESS_FILE, progress);
        }

        console.log('\n--- Scraping Complete ---');
        console.log('All product URLs scraped and saved.');
        fs.unlinkSync(PROGRESS_FILE); // Remove progress file on successful completion
        console.log('Progress file removed.');

    } catch (error) {
        console.error('An unhandled error occurred during scraping:', error);
    } finally {
        await browser.close();
        console.log('Browser closed.');
    }
}

// Start the scraping process
scrapeProducts();
