const puppeteer = require('puppeteer');
const fs = require('fs');

async function scrapeDarbyDentalCategories() {
    const baseURL = 'https://www.darbydental.com/';
    const categories = [];

    // Launch a headless browser
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();

    try {
        // Navigate to the base URL and wait for it to load
        console.log(`Navigating to ${baseURL}...`);
        await page.goto(baseURL, { waitUntil: 'networkidle2' });
        console.log('Page loaded.');

        // Add this for browser console logs, useful for general debugging
        page.on('console', msg => console.log('BROWSER CONSOLE:', msg.text()));

        // Extract category names and URLs
        const categoryData = await page.evaluate((baseURL) => {
            const data = [];
            const categoriesTable = document.querySelector('#Categories2_siteCategories');

            if (categoriesTable) {
                // Select all <a> tags that match your criteria
                const anchors = categoriesTable.querySelectorAll('a[id^="Categories2_siteCategories"]');
                console.log(`Found ${anchors.length} anchor tags.`);

                anchors.forEach(anchor => {
                    const relativeUrl = anchor.getAttribute('href');
                    const url = new URL(relativeUrl, baseURL).href; // Construct absolute URL

                    // *** THE CRUCIAL CHANGE IS HERE ***
                    // Find the immediately preceding sibling <input type="hidden">
                    // whose 'name' attribute starts with 'ctl00$Categories2$siteCategories$'
                    // and get its 'value'.
                    let name = '';
                    const parentLi = anchor.closest('li'); // Find the parent <li> first

                    if (parentLi) {
                        // Look for an input[type="hidden"] that is a child of this <li>
                        // and specifically has an id like 'Categories2_siteCategories_hfName_X'
                        // You mentioned it's a sibling that comes just above, which means it's a child of the same parent (<li>)
                        // and it comes before the anchor.
                        const hiddenInput = parentLi.querySelector('input[type="hidden"][id^="Categories2_siteCategories_hfName_"]');

                        if (hiddenInput) {
                            name = hiddenInput.value.trim().replace(/\s+/g, ' ');
                        } else {
                            // Fallback if the hidden input isn't found (though it should be)
                            // This uses the anchor's aria-label or innerText as a last resort
                            name = anchor.getAttribute('aria-label') || anchor.innerText.trim().replace(/\s+/g, ' ');
                        }
                    } else {
                        // Fallback if no <li> parent is found
                        name = anchor.getAttribute('aria-label') || anchor.innerText.trim().replace(/\s+/g, ' ');
                    }

                    // Only push if a valid name was found (should always be now)
                    if (name) {
                        data.push({ name, url });
                    }
                });
            }
            return data;
        }, baseURL);

        categories.push(...categoryData);
        console.log(`Successfully extracted ${categories.length} categories.`);

        // Save the categories to cats.json
        fs.writeFileSync('cats.json', JSON.stringify(categories, null, 2));
        console.log('Categories saved to cats.json');

    } catch (error) {
        console.error('An error occurred during scraping:', error);
    } finally {
        // Close the browser
        await browser.close();
        console.log('Browser closed.');
    }
}

scrapeDarbyDentalCategories();