// Import necessary modules
const fs = require('fs');
const path = require('path');

// Define paths for input and output files
const productsFilePath = path.resolve(__dirname, 'benco_products_urls.json');
const categoryTotalsFilePath = path.resolve(__dirname, 'catotal.json');

// Function to load JSON file
function loadJsonFile(filePath, defaultValue = {}) {
    if (fs.existsSync(filePath)) {
        try {
            return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        } catch (error) {
            console.error(`Error reading ${filePath}:`, error);
            return defaultValue;
        }
    }
    return defaultValue;
}

// Function to save JSON file
function saveJsonFile(filePath, data) {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
}

async function countCategoryProducts() {
    console.log(`Reading product data from: ${productsFilePath}`);
    const productsData = loadJsonFile(productsFilePath);

    if (Object.keys(productsData).length === 0) {
        console.warn('No product data found in benco_products_urls.json. Please ensure the scraping script has run successfully.');
        return;
    }

    const categoryTotals = {};
    let grandTotalProducts = 0; // Initialize grand total

    for (const categoryName in productsData) {
        if (Object.hasOwnProperty.call(productsData, categoryName)) {
            const productUrls = productsData[categoryName];
            if (Array.isArray(productUrls)) {
                const currentCategoryCount = productUrls.length;
                categoryTotals[categoryName] = currentCategoryCount;
                grandTotalProducts += currentCategoryCount; // Add to grand total
                console.log(`Category: "${categoryName}" - Total Products: ${currentCategoryCount}`);
            } else {
                console.warn(`Skipping "${categoryName}" as its value is not an array.`);
            }
        }
    }

    // Add the grand total to the categoryTotals object
    categoryTotals['Grand Total Products'] = grandTotalProducts;
    console.log(`\nGrand Total Products Across All Categories: ${grandTotalProducts}`);

    saveJsonFile(categoryTotalsFilePath, categoryTotals);
    console.log(`\nCategory totals successfully saved to ${categoryTotalsFilePath}`);
}

// Run the counter
countCategoryProducts();
