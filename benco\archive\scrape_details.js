// Import necessary modules
const puppeteer = require('puppeteer');
const fs = require('fs');
const readline = require('readline');
const path = require('path');

// Define paths for input and output files
const BASE_URL = "https://shop.benco.com";
const productsUrlsFilePath = path.resolve(__dirname, 'benco_products_urls.json');
const bencoJsonPath = path.resolve(__dirname, 'benco.json');
const missedUrlsFilePath = path.resolve(__dirname, 'missed.json'); // New file for missed URLs

// User data and progress file paths as specified by the user
const userDataDir = path.resolve("./user_data");
const progressFile = path.join(userDataDir, "scraping_progress001.json");

// Function to prompt the user for input in the terminal
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

// Function to load JSON file
function loadJsonFile(filePath, defaultValue = {}) {
    if (fs.existsSync(filePath)) {
        try {
            return JSON.parse(fs.readFileSync(filePath, 'utf8'));
        } catch (error) {
            console.error(`Error reading ${filePath}:`, error);
            return defaultValue;
        }
    }
    return defaultValue;
}

// Function to save JSON file
function saveJsonFile(filePath, data) {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
}

/**
 * Helper function to retry an asynchronous operation on TimeoutError.
 * @param {Function} operation - The asynchronous function to execute.
 * @param {number} maxRetries - Maximum number of retries.
 * @param {number} delayMs - Delay in milliseconds between retries.
 * @returns {Promise<any>} The result of the operation.
 * @throws {Error} If the operation fails after all retries or for a non-TimeoutError.
 */
async function retryOperation(operation, maxRetries = 3, delayMs = 5000) {
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            if (error.name === 'TimeoutError') {
                console.warn(`Operation timed out (attempt ${i + 1}/${maxRetries + 1}). Retrying in ${delayMs / 1000} seconds...`);
                if (i < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, delayMs)); // Wait before retrying
                } else {
                    // Re-throw a specific error message when retries are exhausted for easier catching
                    throw new Error(`Operation failed after ${maxRetries + 1} attempts due to TimeoutError.`);
                }
            } else {
                throw error; // Re-throw other types of errors immediately
            }
        }
    }
}

async function scrapeProductDetails() {
    let browser;
    // Ensure user data directory exists
    await fs.promises.mkdir(userDataDir, { recursive: true }).catch(console.error);

    // Load existing scraped product data (benco.json)
    const bencoJson = loadJsonFile(bencoJsonPath, []);

    // Load progress data
    let progress = loadJsonFile(progressFile, { currentCategoryName: null, currentProductUrlIndex: -1 });

    // Initialize missed URLs array
    let missedUrls = loadJsonFile(missedUrlsFilePath, []);

    try {
        console.log(`Launching browser with user data directory: ${userDataDir}`);
        browser = await puppeteer.launch({
            headless: true, // Set to true for background execution
            userDataDir: userDataDir, // This will save/load your session
            args: [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--start-maximized" // Launch browser maximized
            ],
            defaultViewport: null // This allows Puppeteer to use the browser's default viewport size (often maximized)
        });
        const page = await browser.newPage();
        page.setDefaultNavigationTimeout(60000); // 60 seconds timeout for navigation

        // Initial navigation and manual login prompt
        console.log('Navigating to base URL for potential login: ' + BASE_URL);
        await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
        console.log('Page loaded. Please log in manually in the browser if required.');
        await askQuestion('Press Enter in this terminal AFTER you have successfully logged in to the website and the page has loaded. ');
        console.log('Login confirmed. Continuing with scraping...');

        // Load product URLs from benco_products_urls.json
        const allProductUrlsByCategory = loadJsonFile(productsUrlsFilePath);
        if (Object.keys(allProductUrlsByCategory).length === 0) {
            console.error('No product URLs found in benco_products_urls.json. Please ensure the previous scraping script has run successfully.');
            return;
        }

        const categoryNames = Object.keys(allProductUrlsByCategory);
        let startCategoryIndex = 0;
        let startProductIndex = 0;

        // Determine resume point from progress file
        if (progress.currentCategoryName && progress.currentProductUrlIndex !== -1) {
            startCategoryIndex = categoryNames.indexOf(progress.currentCategoryName);
            if (startCategoryIndex === -1) {
                console.warn(`Last tracked category '${progress.currentCategoryName}' not found in current product URLs. Starting from the beginning.`);
                startCategoryIndex = 0;
            } else {
                startProductIndex = progress.currentProductUrlIndex + 1; // Resume from the next URL
                console.log(`Resuming from category: "${progress.currentCategoryName}", product index: ${startProductIndex}`);
            }
        } else {
            console.log('No previous progress found. Starting from the first category and first product.');
        }

        // Loop through each category
        for (let i = startCategoryIndex; i < categoryNames.length; i++) {
            const categoryName = categoryNames[i];
            const productUrls = allProductUrlsByCategory[categoryName];

            console.log(`\n--- Processing Category: ${categoryName} (${productUrls.length} products) ---`);

            // Loop through each product URL in the current category
            for (let j = (i === startCategoryIndex ? startProductIndex : 0); j < productUrls.length; j++) {
                const relativeProductUrl = productUrls[j];
                const fullProductUrl = BASE_URL + relativeProductUrl;

                console.log(`Scraping product ${j + 1}/${productUrls.length} in "${categoryName}": ${fullProductUrl}`);

                try {
                    // Navigate to product page with retry logic
                    await retryOperation(async () => {
                        await page.goto(fullProductUrl, { waitUntil: 'domcontentloaded', timeout: 60000 }); // Use domcontentloaded for faster load
                        await new Promise(resolve => setTimeout(resolve, 2000)); // Small delay for content to render
                    }, 3, 5000); // 3 retries, 5 sec delay

                    // Scrape product details
                    const productDetails = await page.evaluate(() => {
                        const getName = () => document.querySelector('h1.product-name')?.innerText.trim() || null;
                        const getBrand = () => {
                            const brandSpan = document.querySelector('span[itemprop="brand"]');
                            return brandSpan?.innerText.trim() || null;
                        };
                        const getMpn = () => {
                            const brandSpan = document.querySelector('span[itemprop="brand"]');
                            const mpnSpan = brandSpan ? brandSpan.nextElementSibling : null;
                            return mpnSpan?.innerText.trim() || null;
                        };
                        const getBreadcrumb = (index) => {
                            const breadcrumbLis = document.querySelectorAll('.breadcrumb-bar ul li');
                            return breadcrumbLis[index]?.innerText.trim() || null;
                        };
                        const getDescription = () => document.querySelector('p.product-description')?.innerText.trim() || null;
                        const getPrice = () => document.querySelector('h3.selling-price')?.innerText.trim() || null;

                        return {
                            name: getName(),
                            brand: getBrand(),
                            mpn: getMpn(),
                            maincat: getBreadcrumb(1), // Second li (index 1)
                            subcat: getBreadcrumb(2),  // Third li (index 2)
                            description: getDescription(),
                            price: getPrice(),
                        };
                    });

                    // Add the original relative URL to the product object for traceability
                    productDetails.productUrl = relativeProductUrl;

                    bencoJson.push(productDetails);
                    saveJsonFile(bencoJsonPath, bencoJson);
                    console.log(`Saved product details for: ${productDetails.name}`);

                    // Update progress after successfully scraping and saving a product
                    progress.currentCategoryName = categoryName;
                    progress.currentProductUrlIndex = j;
                    saveJsonFile(progressFile, progress);

                } catch (error) {
                    console.error(`Failed to scrape product '${fullProductUrl}' after retries. Skipping this product. Error: ${error.message}`);
                    // Add the failed URL to the missedUrls array
                    missedUrls.push(fullProductUrl);
                    saveJsonFile(missedUrlsFilePath, missedUrls); // Save immediately
                    // Continue to the next product if a specific product fails
                }
            }
            // Reset product index for the next category
            startProductIndex = 0;
        }

        console.log('\n--- Product details scraping completed for all categories ---');
        if (missedUrls.length > 0) {
            console.log(`\n--- ${missedUrls.length} URLs were missed and saved to missed.json ---`);
        }

    } catch (error) {
        console.error('An unhandled error occurred during scraping:', error);
    } finally {
        if (browser) {
            await browser.close();
            console.log('Browser closed.');
        }
        rl.close();
    }
}

// Run the scraper
scrapeProductDetails();
