const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const readline = require('readline'); // For user input to pause for login

// --- Configuration ---
const BASE_URL = 'https://www.darbydental.com';
const USER_DATA_DIR = path.join(__dirname, 'puppeteer_user_data'); // Directory to store session data
const PRODUCTS_INPUT_FILE = 'producturls.json'; // Input file with product URLs (from previous scraper)
const PRODUCTS_OUTPUT_FILE = 'darby.json'; // Output file for scraped product details
const PROGRESS_FILE = 'product_details_progress.json'; // File to save scraping progress

// --- Memory Optimization Configuration ---
const BATCH_SIZE = 50; // Process URLs in batches to manage memory
const MEMORY_CHECK_INTERVAL = 10; // Check memory every N URLs
const MAX_MEMORY_MB = 1024; // Restart browser if memory exceeds this (MB)
const SAVE_INTERVAL = 25; // Save progress every N URLs instead of every URL

// --- Global Data Storage ---
let currentBatchProducts = []; // Stores current batch of scraped products (cleared after each batch)
let totalProductsScraped = 0; // Counter for total products scraped
let progress = {
    currentCategoryIndex: 0,
    currentProductUrlIndex: 0
};

// --- Helper Functions ---

/**
 * Gets current memory usage in MB
 * @returns {object} Memory usage statistics
 */
function getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
        rss: Math.round(usage.rss / 1024 / 1024), // Resident Set Size in MB
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // Heap used in MB
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // Total heap in MB
        external: Math.round(usage.external / 1024 / 1024) // External memory in MB
    };
}

/**
 * Logs memory usage with context
 * @param {string} context - Context description
 */
function logMemoryUsage(context) {
    const memory = getMemoryUsage();
    console.log(`[MEMORY] ${context} - RSS: ${memory.rss}MB, Heap: ${memory.heapUsed}/${memory.heapTotal}MB, External: ${memory.external}MB`);
}

/**
 * Forces garbage collection if available
 */
function forceGarbageCollection() {
    if (global.gc) {
        global.gc();
        console.log('[MEMORY] Forced garbage collection');
    }
}

/**
 * Loads data from a JSON file.
 * @param {string} filePath - Path to the JSON file.
 * @returns {object|null} Parsed JSON data or null if file doesn't exist/is invalid.
 */
function loadJson(filePath) {
    if (fs.existsSync(filePath)) {
        try {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error(`Error reading or parsing ${filePath}:`, error);
            return null;
        }
    }
    return null;
}

/**
 * Saves data to a JSON file.
 * @param {string} filePath - Path to the JSON file.
 * @param {object} data - Data to save.
 */
function saveJson(filePath, data) {
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`Data saved to ${filePath}`);
    } catch (error) {
        console.error(`Error saving to ${filePath}:`, error);
    }
}

/**
 * Appends products to the output file efficiently
 * @param {Array} products - Array of products to append
 */
function appendProductsToFile(products) {
    if (!products || products.length === 0) return;

    try {
        // Load existing data
        let existingProducts = [];
        if (fs.existsSync(PRODUCTS_OUTPUT_FILE)) {
            const existingData = loadJson(PRODUCTS_OUTPUT_FILE);
            if (existingData && Array.isArray(existingData)) {
                existingProducts = existingData;
            }
        }

        // Append new products
        existingProducts.push(...products);

        // Save back to file
        fs.writeFileSync(PRODUCTS_OUTPUT_FILE, JSON.stringify(existingProducts, null, 2), 'utf8');
        console.log(`Appended ${products.length} products to ${PRODUCTS_OUTPUT_FILE}. Total: ${existingProducts.length}`);

        // Update global counter
        totalProductsScraped = existingProducts.length;

    } catch (error) {
        console.error(`Error appending products to ${PRODUCTS_OUTPUT_FILE}:`, error);
    }
}

/**
 * Prompts the user to press a key to continue.
 * @param {string} message - Message to display to the user.
 * @returns {Promise<void>} A promise that resolves when the user presses a key.
 */
async function promptForInput(message) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    return new Promise(resolve => {
        rl.question(message, () => {
            rl.close();
            resolve();
        });
    });
}

/**
 * Creates a new browser instance with optimized settings
 * @returns {Promise<object>} Browser instance
 */
async function createOptimizedBrowser() {
    logMemoryUsage('Before browser launch');

    const browser = await puppeteer.launch({
        headless: true,
        defaultViewport: null,
        userDataDir: USER_DATA_DIR,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage', // Overcome limited resource problems
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--memory-pressure-off', // Disable memory pressure notifications
            '--max_old_space_size=2048' // Increase V8 heap size
        ]
    });

    logMemoryUsage('After browser launch');
    return browser;
}

/**
 * Safely closes browser and cleans up resources
 * @param {object} browser - Browser instance to close
 */
async function closeBrowserSafely(browser) {
    if (browser) {
        try {
            const pages = await browser.pages();
            for (const page of pages) {
                await page.close();
            }
            await browser.close();
            logMemoryUsage('After browser close');
            forceGarbageCollection();
        } catch (error) {
            console.error('Error closing browser:', error);
        }
    }
}

// --- Main Scraping Logic ---

async function scrapeProductDetails() {
    logMemoryUsage('Script start');

    // Load existing scraped products count (don't load all data into memory)
    const existingScrapedProducts = loadJson(PRODUCTS_OUTPUT_FILE);
    if (existingScrapedProducts && Array.isArray(existingScrapedProducts)) {
        totalProductsScraped = existingScrapedProducts.length;
        console.log(`Found ${totalProductsScraped} existing product details in ${PRODUCTS_OUTPUT_FILE}`);
        // Don't load all products into memory - we'll append new ones
    }

    const loadedProgress = loadJson(PROGRESS_FILE);
    if (loadedProgress && typeof loadedProgress.currentCategoryIndex === 'number' && typeof loadedProgress.currentProductUrlIndex === 'number') {
        progress = { ...progress, ...loadedProgress };
        console.log(`Resuming from progress: Category Index ${progress.currentCategoryIndex}, Product URL Index ${progress.currentProductUrlIndex}`);
    }

    const productUrlsByCategory = loadJson(PRODUCTS_INPUT_FILE);
    if (!productUrlsByCategory || Object.keys(productUrlsByCategory).length === 0) {
        console.error(`No product URLs found in ${PRODUCTS_INPUT_FILE}. Please run the product URL scraper first.`);
        return;
    }

    // Convert the object of categories to an array for easier iteration
    const categories = Object.entries(productUrlsByCategory);
    console.log(`Total categories: ${categories.length}, Total URLs to process: ${categories.reduce((sum, [, urls]) => sum + urls.length, 0)}`);

    logMemoryUsage('After data loading');

    // Launch browser with optimized settings
    let browser = await createOptimizedBrowser();
    let page = await browser.newPage();

    // Optimize page settings for memory efficiency
    await page.setRequestInterception(true);
    page.on('request', (req) => {
        // Block unnecessary resources to save memory
        if (req.resourceType() === 'image' || req.resourceType() === 'stylesheet' || req.resourceType() === 'font') {
            req.abort();
        } else {
            req.continue();
        }
    });

    // Listen for console messages from the browser context for debugging
    // page.on('console', msg => console.log('BROWSER CONSOLE:', msg.text()));
    // page.on('pageerror', err => console.error('PAGE ERROR:', err.toString()));

    try {
        // Navigate to base URL and wait for user to log in
        console.log(`Navigating to ${BASE_URL} for login...`);
        await page.goto(BASE_URL, { waitUntil: 'domcontentloaded', timeout: 60000 });
        await promptForInput('Please log in to Darby Dental in the browser window, then press Enter here to continue scraping...');
        console.log('Login confirmed. Starting scraping...');

        logMemoryUsage('After login');

        let urlsProcessedSinceLastSave = 0;
        let urlsProcessedSinceLastMemoryCheck = 0;

        // Loop through each category
        for (let i = progress.currentCategoryIndex; i < categories.length; i++) {
            const [categoryName, productUrls] = categories[i];
            console.log(`\n--- Scraping Products for Category: "${categoryName}" (${productUrls.length} URLs) ---`);
            logMemoryUsage(`Starting category ${categoryName}`);

            // Determine starting product URL index for this category
            let startProductUrlIndex = 0;
            if (i === progress.currentCategoryIndex) {
                startProductUrlIndex = progress.currentProductUrlIndex;
                console.log(`Resuming category "${categoryName}" from product URL index ${startProductUrlIndex}`);
            } else {
                // Reset product URL index for new category
                progress.currentProductUrlIndex = 0;
            }

            // Loop through each product URL within the current category
            for (let j = startProductUrlIndex; j < productUrls.length; j++) {
                const productUrl = productUrls[j];
                console.log(`Visiting product URL (${j + 1}/${productUrls.length}): ${productUrl}`);

                try {
                    // Check memory usage periodically
                    urlsProcessedSinceLastMemoryCheck++;
                    if (urlsProcessedSinceLastMemoryCheck >= MEMORY_CHECK_INTERVAL) {
                        const memory = getMemoryUsage();
                        logMemoryUsage(`After ${urlsProcessedSinceLastMemoryCheck} URLs`);

                        // If memory usage is too high, restart browser
                        if (memory.rss > MAX_MEMORY_MB) {
                            console.log(`[MEMORY] Memory usage (${memory.rss}MB) exceeds limit (${MAX_MEMORY_MB}MB). Restarting browser...`);
                            await closeBrowserSafely(browser);
                            forceGarbageCollection();

                            browser = await createOptimizedBrowser();
                            page = await browser.newPage();

                            // Re-setup page optimization
                            await page.setRequestInterception(true);
                            page.on('request', (req) => {
                                if (req.resourceType() === 'image' || req.resourceType() === 'stylesheet' || req.resourceType() === 'font') {
                                    req.abort();
                                } else {
                                    req.continue();
                                }
                            });
                        }
                        urlsProcessedSinceLastMemoryCheck = 0;
                    }

                    await page.goto(productUrl, { waitUntil: 'domcontentloaded', timeout: 60000 }); // domcontentloaded is often faster for product pages
                    await page.waitForSelector('#MainContent_lblName', { timeout: 30000 }); // Wait for a key element on the product page

                    const productDetails = await page.evaluate((currentUrl) => {
                        const commonInfo = {};
                        commonInfo.url = currentUrl;

                        // Extract common info
                        const groupNameSpan = document.querySelector('#MainContent_lblName');
                        if (groupNameSpan) commonInfo['group-name'] = groupNameSpan.innerText.trim();

                        const groupDescSpan = document.querySelector('#MainContent_lblDescription');
                        if (groupDescSpan) commonInfo['group-desc'] = groupDescSpan.innerText.trim();

                        const manufacturerLink = document.querySelector('#MainContent_lblManufacturer');
                        if (manufacturerLink) commonInfo.manufacturer = manufacturerLink.innerText.trim();

                        const breadcrumbs = document.querySelectorAll('ul.breadcrumb li.breadcrumb-item');
                        if (breadcrumbs.length >= 2) {
                            commonInfo.maincat = breadcrumbs[1].innerText.trim();
                        }
                        if (breadcrumbs.length >= 3) {
                            commonInfo.subcat = breadcrumbs[2].innerText.trim();
                        }

                        const variations = [];
                        const variationsTable = document.querySelector('#MainContent_gvAdditonalProduct');

                        if (variationsTable) {
                            // Select only rows with class "pdpHelltPrimary"
                            const variationRows = variationsTable.querySelectorAll('tr.pdpHelltPrimary');
                            variationRows.forEach(row => {
                                const tds = row.querySelectorAll('td');
                                if (tds.length >= 7) { // Ensure enough columns exist
                                    const variation = { ...commonInfo }; // Start with common info for each variation

                                    // --- UPDATED SELECTORS FOR PRODUCTNUMBER, NAME, PRICE ---
                                    const productNumberSpan = row.querySelector('span[id^="MainContent_gvAdditonalProduct_hlProdNo"]');
                                    if (productNumberSpan) {
                                        variation.productnumber = productNumberSpan.innerText.trim();
                                    } else {
                                        // Fallback to original td[1] if new selector fails
                                        variation.productnumber = tds[1] ? tds[1].innerText.trim() : '';
                                    }

                                    const nameSpan = row.querySelector('span[id^="MainContent_gvAdditonalProduct_lblDesc"]');
                                    if (nameSpan) {
                                        variation.name = nameSpan.innerText.trim();
                                    } else {
                                        // Fallback to original td[2] if new selector fails
                                        variation.name = tds[2] ? tds[2].innerText.trim() : '';
                                    }

                                    const priceSpan = row.querySelector('span[id^="MainContent_gvAdditonalProduct_lblPrice"]');
                                    if (priceSpan) {
                                        variation.price = priceSpan.innerText.trim();
                                    } else {
                                        // Fallback to original td[6] if new selector fails
                                        variation.price = tds[6] ? tds[6].innerText.trim() : '';
                                    }
                                    // --- END UPDATED SELECTORS ---

                                    variations.push(variation);
                                }
                            });
                        } else {
                            // Handle cases where there might be no variations table, but still a single product
                            // In this case, the commonInfo itself might represent the single product
                            // We'll push the commonInfo as a single product if no variations table is found
                            console.log('No variations table found. Treating as single product.');
                            variations.push(commonInfo);
                        }

                        return variations; // Return an array of product objects (one per variation)
                    }, productUrl); // Pass current productUrl to the browser context

                    if (productDetails.length > 0) {
                        currentBatchProducts.push(...productDetails);
                        totalProductsScraped += productDetails.length;
                        console.log(`Scraped ${productDetails.length} variations for ${productUrl}. Batch: ${currentBatchProducts.length}, Total: ${totalProductsScraped}`);
                    } else {
                        console.warn(`No product details or variations found for ${productUrl}.`);
                    }

                    // Update progress
                    progress.currentCategoryIndex = i;
                    progress.currentProductUrlIndex = j + 1; // Next URL to scrape
                    urlsProcessedSinceLastSave++;

                    // Save progress and batch data periodically instead of every URL
                    if (urlsProcessedSinceLastSave >= SAVE_INTERVAL || currentBatchProducts.length >= BATCH_SIZE) {
                        console.log(`[BATCH] Saving batch of ${currentBatchProducts.length} products...`);
                        appendProductsToFile(currentBatchProducts);
                        saveJson(PROGRESS_FILE, progress);

                        // Clear batch and reset counter
                        currentBatchProducts = [];
                        urlsProcessedSinceLastSave = 0;

                        // Force garbage collection after batch save
                        forceGarbageCollection();
                        logMemoryUsage('After batch save');
                    }

                } catch (productError) {
                    console.error(`Error scraping product URL ${productUrl}:`, productError.message);
                    // Continue to next product URL even if one fails
                }
            }

            // Save any remaining products in the current batch at end of category
            if (currentBatchProducts.length > 0) {
                console.log(`[BATCH] Saving final batch of ${currentBatchProducts.length} products for category "${categoryName}"...`);
                appendProductsToFile(currentBatchProducts);
                currentBatchProducts = [];
                forceGarbageCollection();
            }

            console.log(`Finished scraping all products for category: "${categoryName}".`);
            logMemoryUsage(`Completed category ${categoryName}`);

            // Reset product URL index for the next category
            progress.currentProductUrlIndex = 0;
            // Save progress for the completed category
            progress.lastCompletedCategoryIndex = i;
            saveJson(PROGRESS_FILE, progress);
        }

        // Save any final remaining products
        if (currentBatchProducts.length > 0) {
            console.log(`[BATCH] Saving final batch of ${currentBatchProducts.length} products...`);
            appendProductsToFile(currentBatchProducts);
            currentBatchProducts = [];
        }

        console.log('\n--- Product Details Scraping Complete ---');
        console.log(`All product details scraped and saved to ${PRODUCTS_OUTPUT_FILE}. Total products: ${totalProductsScraped}`);
        fs.unlinkSync(PROGRESS_FILE); // Remove progress file on successful completion
        console.log('Progress file removed.');

        logMemoryUsage('Scraping complete');

    } catch (error) {
        console.error('An unhandled error occurred during scraping:', error);

        // Save any remaining products before exiting
        if (currentBatchProducts.length > 0) {
            console.log(`[EMERGENCY] Saving ${currentBatchProducts.length} products before exit...`);
            appendProductsToFile(currentBatchProducts);
        }

        logMemoryUsage('Error occurred');
    } finally {
        await closeBrowserSafely(browser);
        forceGarbageCollection();
        logMemoryUsage('Final cleanup');
        console.log('Browser closed and resources cleaned up.');
    }
}

// Display memory optimization info
console.log('='.repeat(80));
console.log('MEMORY-OPTIMIZED WEB SCRAPER');
console.log('='.repeat(80));
console.log('Memory optimizations enabled:');
console.log('- Batch processing with automatic saves');
console.log('- Browser restart on high memory usage');
console.log('- Resource blocking (images, CSS, fonts)');
console.log('- Garbage collection hints');
console.log('');
console.log('For best performance, run with: node --expose-gc details.js');
console.log('This enables manual garbage collection for better memory management.');
console.log('='.repeat(80));
console.log('');

// Start the scraping process
scrapeProductDetails();
